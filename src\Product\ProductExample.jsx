import React from 'react';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '../contexts/ThemeContext';
import { createOverrideTheme } from '../theme';
import Product from './Product';

/**
 * Example component showing how to use the Product component
 * with your existing theme system
 */
const ProductExample = () => {
  return (
    <ThemeProvider>
      <ThemeConsumer />
    </ThemeProvider>
  );
};

const ThemeConsumer = () => {
  const { getMuiPaletteConfig } = useTheme();
  const muiTheme = createOverrideTheme(getMuiPaletteConfig());

  return (
    <MuiThemeProvider theme={muiTheme}>
      <CssBaseline />
      <Product />
    </MuiThemeProvider>
  );
};

// Import useTheme hook
import { useTheme } from '../contexts/ThemeContext';

export default ProductExample;
