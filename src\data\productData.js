// Aftermarket Network Commerce Product Data Structure
export const productCategories = {
  AUTOMOTIVE: 'automotive',
  ELECTRONICS: 'electronics',
  INDUSTRIAL: 'industrial',
  MARINE: 'marine',
  AEROSPACE: 'aerospace'
};

export const productTypes = {
  REPLACEMENT_PARTS: 'replacement_parts',
  PERFORMANCE_UPGRADES: 'performance_upgrades',
  ACCESSORIES: 'accessories',
  TOOLS: 'tools',
  CONSUMABLES: 'consumables'
};

export const compatibilityTypes = {
  UNIVERSAL: 'universal',
  VEHICLE_SPECIFIC: 'vehicle_specific',
  BRAND_SPECIFIC: 'brand_specific',
  MODEL_SPECIFIC: 'model_specific'
};

// Sample aftermarket product data
export const sampleProducts = [
  {
    id: 'AM001',
    name: 'High-Performance Air Filter',
    brand: 'ProFlow',
    category: productCategories.AUTOMOTIVE,
    type: productTypes.PERFORMANCE_UPGRADES,
    sku: 'PF-AF-2024-001',
    
    // Pricing
    pricing: {
      msrp: 89.99,
      dealerPrice: 67.49,
      retailPrice: 79.99,
      currency: 'USD',
      discountPercentage: 11
    },
    
    // Inventory
    inventory: {
      inStock: true,
      quantity: 156,
      lowStockThreshold: 25,
      backorderAvailable: true,
      estimatedRestockDate: '2024-02-15'
    },
    
    // Product Details
    description: 'High-flow performance air filter designed for increased horsepower and torque. Features washable and reusable cotton gauze construction with precision-molded rubber gaskets.',
    
    specifications: {
      'Filter Type': 'Cotton Gauze',
      'Flow Rate': '850 CFM',
      'Filtration Efficiency': '99.2%',
      'Dimensions': '14" x 9" x 2"',
      'Weight': '1.2 lbs',
      'Material': 'Cotton/Rubber',
      'Washable': 'Yes',
      'Warranty': '1 Million Mile'
    },
    
    // Compatibility
    compatibility: {
      type: compatibilityTypes.VEHICLE_SPECIFIC,
      vehicles: [
        {
          make: 'Ford',
          model: 'F-150',
          years: [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023],
          engines: ['3.5L V6 EcoBoost', '5.0L V8', '2.7L V6 EcoBoost']
        },
        {
          make: 'Ford',
          model: 'Mustang',
          years: [2018, 2019, 2020, 2021, 2022, 2023],
          engines: ['5.0L V8 GT', '2.3L I4 EcoBoost']
        }
      ],
      oem_numbers: ['FL-820-S', 'CA10755', '16546-3TA0A'],
      interchange_numbers: ['K&N 33-2385', 'WIX 46899', 'FRAM CA10755']
    },
    
    // Media
    media: {
      primaryImage: '/src/images/products/air-filter-primary.jpg',
      images: [
        '/src/images/products/air-filter-primary.jpg',
        '/src/images/products/air-filter-installed.jpg',
        '/src/images/products/air-filter-comparison.jpg',
        '/src/images/products/air-filter-packaging.jpg'
      ],
      videos: [
        {
          title: 'Installation Guide',
          url: '/src/videos/air-filter-install.mp4',
          thumbnail: '/src/images/products/air-filter-install-thumb.jpg'
        }
      ],
      documents: [
        {
          title: 'Installation Instructions',
          url: '/src/documents/air-filter-install.pdf',
          type: 'PDF'
        },
        {
          title: 'Compatibility Chart',
          url: '/src/documents/air-filter-compatibility.pdf',
          type: 'PDF'
        }
      ]
    },
    
    // Features & Benefits
    features: [
      'Increases horsepower by up to 8HP',
      'Improves acceleration response',
      'Washable and reusable - saves money',
      'Million mile limited warranty',
      'Easy 10-minute installation',
      'Precision-molded rubber gaskets',
      'High-flow cotton gauze construction'
    ],
    
    // SEO & Marketing
    seo: {
      metaTitle: 'ProFlow High-Performance Air Filter - Ford F-150 & Mustang',
      metaDescription: 'Boost your Ford\'s performance with ProFlow\'s washable air filter. Increases HP, improves acceleration. Million mile warranty.',
      keywords: ['air filter', 'performance', 'Ford F-150', 'Mustang', 'horsepower', 'washable']
    },
    
    // Reviews & Ratings
    reviews: {
      averageRating: 4.7,
      totalReviews: 342,
      ratingDistribution: {
        5: 234,
        4: 78,
        3: 21,
        2: 6,
        1: 3
      }
    },
    
    // Shipping & Logistics
    shipping: {
      weight: 1.2,
      dimensions: {
        length: 14,
        width: 9,
        height: 2,
        unit: 'inches'
      },
      shippingClass: 'standard',
      hazmat: false,
      freeShippingEligible: true
    },
    
    // Related Products
    relatedProducts: ['AM002', 'AM003', 'AM004'],
    crossSells: ['AM005', 'AM006'],
    upSells: ['AM007', 'AM008'],
    
    // Admin Configuration
    adminConfig: {
      isActive: true,
      isVisible: true,
      isFeatured: false,
      displayOrder: 1,
      createdDate: '2024-01-15T10:30:00Z',
      lastModified: '2024-01-20T14:45:00Z',
      createdBy: '<EMAIL>',
      lastModifiedBy: '<EMAIL>'
    }
  },
  
  // Additional sample products can be added here
  {
    id: 'AM002',
    name: 'Performance Exhaust System',
    brand: 'SoundMax',
    category: productCategories.AUTOMOTIVE,
    type: productTypes.PERFORMANCE_UPGRADES,
    sku: 'SM-EX-2024-002',
    
    pricing: {
      msrp: 599.99,
      dealerPrice: 449.99,
      retailPrice: 529.99,
      currency: 'USD',
      discountPercentage: 12
    },
    
    inventory: {
      inStock: true,
      quantity: 23,
      lowStockThreshold: 10,
      backorderAvailable: true,
      estimatedRestockDate: '2024-02-20'
    },
    
    description: 'Cat-back performance exhaust system with mandrel-bent stainless steel construction. Delivers aggressive sound and improved performance.',
    
    specifications: {
      'Material': 'Stainless Steel',
      'Pipe Diameter': '3 inches',
      'Sound Level': 'Aggressive',
      'Weight': '45 lbs',
      'Finish': 'Polished',
      'Warranty': 'Lifetime'
    },
    
    compatibility: {
      type: compatibilityTypes.VEHICLE_SPECIFIC,
      vehicles: [
        {
          make: 'Chevrolet',
          model: 'Camaro',
          years: [2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023],
          engines: ['6.2L V8 SS', '3.6L V6']
        }
      ]
    },
    
    media: {
      primaryImage: '/src/images/products/placeholder.svg',
      images: [
        '/src/images/products/placeholder.svg',
        '/src/images/products/placeholder.svg',
        '/src/images/products/placeholder.svg'
      ]
    },
    
    features: [
      'Increases horsepower by 15-20HP',
      'Aggressive exhaust note',
      'Mandrel-bent for maximum flow',
      'Stainless steel construction',
      'Lifetime warranty',
      'Bolt-on installation'
    ],
    
    reviews: {
      averageRating: 4.8,
      totalReviews: 156,
      ratingDistribution: {
        5: 124,
        4: 23,
        3: 6,
        2: 2,
        1: 1
      }
    },
    
    shipping: {
      weight: 45,
      dimensions: {
        length: 60,
        width: 12,
        height: 8,
        unit: 'inches'
      },
      shippingClass: 'oversized',
      hazmat: false,
      freeShippingEligible: false
    },
    
    adminConfig: {
      isActive: true,
      isVisible: true,
      isFeatured: true,
      displayOrder: 2,
      createdDate: '2024-01-16T09:15:00Z',
      lastModified: '2024-01-21T11:30:00Z',
      createdBy: '<EMAIL>',
      lastModifiedBy: '<EMAIL>'
    }
  }
];

// Admin configuration options
export const adminConfigOptions = {
  visibilityFields: [
    { key: 'isActive', label: 'Product Active', type: 'boolean' },
    { key: 'isVisible', label: 'Visible to Customers', type: 'boolean' },
    { key: 'isFeatured', label: 'Featured Product', type: 'boolean' },
    { key: 'displayOrder', label: 'Display Order', type: 'number' }
  ],
  
  pricingFields: [
    { key: 'msrp', label: 'MSRP', type: 'currency' },
    { key: 'dealerPrice', label: 'Dealer Price', type: 'currency' },
    { key: 'retailPrice', label: 'Retail Price', type: 'currency' },
    { key: 'discountPercentage', label: 'Discount %', type: 'percentage' }
  ],
  
  inventoryFields: [
    { key: 'quantity', label: 'Stock Quantity', type: 'number' },
    { key: 'lowStockThreshold', label: 'Low Stock Alert', type: 'number' },
    { key: 'backorderAvailable', label: 'Allow Backorders', type: 'boolean' }
  ],
  
  displayFields: [
    { key: 'name', label: 'Product Name', type: 'text' },
    { key: 'description', label: 'Description', type: 'textarea' },
    { key: 'features', label: 'Key Features', type: 'array' },
    { key: 'specifications', label: 'Specifications', type: 'object' }
  ]
};

export default sampleProducts;
