import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import Product from './Product';

// Mock theme for testing
const mockTheme = createTheme({
  palette: {
    primary: { main: '#0F5FDC' },
    secondary: { main: '#2ECCCB' },
    error: { main: '#dc3545' },
    text: { primary: '#000032', secondary: '#17707F' }
  }
});

// Wrapper component for testing
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={mockTheme}>
    {children}
  </ThemeProvider>
);

describe('Product Component', () => {
  test('renders product name and brand', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('ATHENA NECKLACE CASE')).toBeInTheDocument();
    expect(screen.getByText('Black')).toBeInTheDocument();
  });

  test('displays product price correctly', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('79.99 EUR')).toBeInTheDocument();
    expect(screen.getByText('55.99 EUR')).toBeInTheDocument();
  });

  test('shows rating and review count', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('234 reviews')).toBeInTheDocument();
  });

  test('displays free shipping badge', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('JE HEBT GRATIS LEVERING BEREIKT!')).toBeInTheDocument();
  });

  test('allows variant selection', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    const selectElement = screen.getByLabelText('Select Variant');
    expect(selectElement).toBeInTheDocument();
  });

  test('shows add to cart button', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('VOEG TOE AAN WINKELMANDJE')).toBeInTheDocument();
  });

  test('displays breadcrumbs navigation', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('IDEAL')).toBeInTheDocument();
    expect(screen.getByText('TELEFOONHOESJES')).toBeInTheDocument();
    expect(screen.getByText('NECKLACE CASES')).toBeInTheDocument();
  });

  test('switches between tabs', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    const reviewsTab = screen.getByText('REVIEWS');
    const detailsTab = screen.getByText('DETAILS');
    
    expect(reviewsTab).toBeInTheDocument();
    expect(detailsTab).toBeInTheDocument();
    
    // Click on details tab
    fireEvent.click(detailsTab);
    expect(screen.getByText('Product Details')).toBeInTheDocument();
  });

  test('displays related products', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    expect(screen.getByText('Related Products')).toBeInTheDocument();
    expect(screen.getByText('Magnet Wallet+')).toBeInTheDocument();
    expect(screen.getByText('Car Mount')).toBeInTheDocument();
  });

  test('handles image navigation', () => {
    render(
      <TestWrapper>
        <Product />
      </TestWrapper>
    );
    
    // Check if navigation arrows are present
    const prevButton = screen.getByRole('button', { name: /arrow/i });
    expect(prevButton).toBeInTheDocument();
  });
});
