import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert
} from '@mui/material';
import { sampleProducts } from '../data/productData';
import Product from './Product';

/**
 * Demo component to showcase different product configurations
 * and admin functionality
 */
const ProductDemo = () => {
  const [selectedProductId, setSelectedProductId] = useState('AM001');
  const [isAdmin, setIsAdmin] = useState(false);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Demo Controls */}
      <Card sx={{ mb: 4, p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Aftermarket Network Commerce - Product Demo
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          This demo showcases the product page with admin configuration capabilities.
          Toggle between Customer and Admin modes to see different features.
        </Alert>

        <Grid container spacing={3}>
          {/* Product Selection */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Select Product:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {sampleProducts.map((product) => (
                <Chip
                  key={product.id}
                  label={`${product.id} - ${product.name}`}
                  onClick={() => setSelectedProductId(product.id)}
                  color={selectedProductId === product.id ? 'primary' : 'default'}
                  variant={selectedProductId === product.id ? 'filled' : 'outlined'}
                />
              ))}
            </Box>
          </Grid>

          {/* Mode Selection */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              View Mode:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant={!isAdmin ? 'contained' : 'outlined'}
                onClick={() => setIsAdmin(false)}
                size="large"
              >
                👤 Customer View
              </Button>
              <Button
                variant={isAdmin ? 'contained' : 'outlined'}
                onClick={() => setIsAdmin(true)}
                size="large"
              >
                ⚙️ Admin View
              </Button>
            </Box>
          </Grid>
        </Grid>

        {/* Feature Highlights */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Key Features:
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="subtitle2" gutterBottom>
                  🚗 Vehicle Compatibility
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Advanced make/model/year matching
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="subtitle2" gutterBottom>
                  ⚙️ Admin Controls
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Real-time configuration panel
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="subtitle2" gutterBottom>
                  📊 Inventory Management
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Live stock levels & alerts
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="subtitle2" gutterBottom>
                  🎨 Theme Integration
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Uses your CSS variables
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Card>

      {/* Product Display */}
      <Product productId={selectedProductId} isAdmin={isAdmin} />
    </Container>
  );
};

export default ProductDemo;
