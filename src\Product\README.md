# Aftermarket Network Commerce Product System

A comprehensive product detail page system designed specifically for aftermarket network commerce, featuring advanced admin configuration capabilities and professional automotive/industrial product display.

## 🚀 Features

### **Aftermarket-Specific Functionality**
- **Vehicle Compatibility System**: Advanced compatibility matching with make/model/year/engine
- **OEM Part Number Cross-Reference**: Interchange numbers and OEM equivalents
- **Performance Specifications**: Technical specs relevant to aftermarket products
- **Installation Information**: Professional installation guides and documentation
- **Inventory Management**: Real-time stock levels with low-stock alerts

### **Admin Configuration Panel**
- **Visibility Controls**: Toggle product visibility, featured status, and display order
- **Pricing Management**: Control MSRP, dealer pricing, and discount display
- **Feature Toggles**: Enable/disable compatibility, reviews, installation sections
- **Real-time Preview**: See changes immediately without page refresh
- **Audit Trail**: Track who made changes and when

### **Professional UI/UX**
- **Space-Optimized Layout**: Maximum screen utilization with sticky navigation
- **Responsive Design**: Works perfectly on all device sizes
- **Theme Integration**: Uses your existing CSS variables and MUI theme
- **No Inline Styles**: Clean, maintainable code following your standards
- **Loading States**: Professional loading indicators and error handling

## 📁 File Structure

```
src/
├── Product/
│   ├── Product.jsx           # Main product component with admin panel
│   ├── Product.test.jsx      # Comprehensive test suite
│   ├── ProductExample.jsx    # Integration examples
│   └── README.md            # This documentation
├── data/
│   └── productData.js       # Structured product data & admin config
└── images/
    └── products/            # Product image assets
        ├── placeholder.svg  # Default placeholder
        └── *.jpg           # Product images

```

## 🎯 Usage

### **Customer View**
```jsx
import Product from './Product/Product';

// Display product for customers
<Product productId="AM001" isAdmin={false} />
```

### **Admin View with Configuration**
```jsx
import Product from './Product/Product';

// Display product with admin controls
<Product productId="AM001" isAdmin={true} />
```

### **Dynamic Product Loading**
```jsx
const ProductPage = ({ productId }) => {
  return (
    <Product
      productId={productId}
      isAdmin={userRole === 'admin'}
    />
  );
};
```

## Styling

The component uses CSS variables from `ThemeContext.jsx` for consistent styling:

- `--color-primary-brand`: Primary brand color
- `--color-secondary-brand`: Secondary brand color  
- `--color-status-error`: Error/sale price color
- `--color-status-success`: Success/shipping badge color
- `--theme-border-color`: Border colors
- `--card-shadow`: Card shadows
- `--button-primary-bg-brand`: Button backgrounds

## Props

The component currently uses mock data but can be easily extended to accept props:

```jsx
// Future enhancement
<Product 
  productId={123}
  onAddToCart={handleAddToCart}
  onAddToWishlist={handleWishlist}
/>
```

## Testing

Run the test suite:

```bash
npm test Product.test.jsx
```

The tests cover:
- Component rendering
- User interactions
- Tab switching
- Image navigation
- Price display
- Variant selection

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 18+
- Material-UI 5+
- Material-UI Icons
- Your existing theme system

## Performance Considerations

- Images are lazy-loaded
- Component uses React.memo for optimization
- Minimal re-renders on state changes
- Efficient event handling

## Accessibility

- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management

## Future Enhancements

- [ ] Product zoom modal
- [ ] 360° product view
- [ ] Video support
- [ ] Size guide integration
- [ ] Stock level indicators
- [ ] Real-time pricing updates
- [ ] Social proof badges
- [ ] Recently viewed products
