# Product Component

A comprehensive product detail page component for the Network Commerce website, built with Material-UI components and integrated with the existing theme system.

## Features

### Core Functionality
- **Structured Product Listings**: Complete product information display with name, brand, pricing, and ratings
- **Variants Support**: Dropdown selection for different product variants (iPhone models, etc.)
- **Media Support**: Image gallery with thumbnail navigation and zoom functionality
- **Multi-Currency Support**: Price display with original and sale prices in EUR
- **Color Selection**: Visual color picker for product variants

### User Experience
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Interactive Elements**: Image carousel, tabs, and selection controls
- **Accessibility**: Proper ARIA labels and keyboard navigation support
- **Loading States**: Smooth transitions and hover effects

### E-commerce Features
- **Add to Cart**: Primary action button with shopping cart integration
- **Wishlist & Share**: Secondary action buttons for user engagement
- **Related Products**: Cross-selling recommendations with pricing
- **Product Reviews**: Tabbed interface for customer reviews and ratings
- **Product Details**: Comprehensive specifications and features list

## Component Structure

```
src/Product/
├── Product.jsx           # Main product component
├── Product.test.jsx      # Unit tests
├── ProductExample.jsx    # Integration example
└── README.md            # This documentation
```

## Usage

### Basic Usage

```jsx
import Product from './Product/Product';
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      <Product />
    </ThemeProvider>
  );
}
```

### With Custom Theme

```jsx
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { createOverrideTheme } from './theme';
import { useTheme } from './contexts/ThemeContext';

const ProductPage = () => {
  const { getMuiPaletteConfig } = useTheme();
  const muiTheme = createOverrideTheme(getMuiPaletteConfig());

  return (
    <MuiThemeProvider theme={muiTheme}>
      <Product />
    </MuiThemeProvider>
  );
};
```

## Styling

The component uses CSS variables from `ThemeContext.jsx` for consistent styling:

- `--color-primary-brand`: Primary brand color
- `--color-secondary-brand`: Secondary brand color  
- `--color-status-error`: Error/sale price color
- `--color-status-success`: Success/shipping badge color
- `--theme-border-color`: Border colors
- `--card-shadow`: Card shadows
- `--button-primary-bg-brand`: Button backgrounds

## Props

The component currently uses mock data but can be easily extended to accept props:

```jsx
// Future enhancement
<Product 
  productId={123}
  onAddToCart={handleAddToCart}
  onAddToWishlist={handleWishlist}
/>
```

## Testing

Run the test suite:

```bash
npm test Product.test.jsx
```

The tests cover:
- Component rendering
- User interactions
- Tab switching
- Image navigation
- Price display
- Variant selection

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 18+
- Material-UI 5+
- Material-UI Icons
- Your existing theme system

## Performance Considerations

- Images are lazy-loaded
- Component uses React.memo for optimization
- Minimal re-renders on state changes
- Efficient event handling

## Accessibility

- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management

## Future Enhancements

- [ ] Product zoom modal
- [ ] 360° product view
- [ ] Video support
- [ ] Size guide integration
- [ ] Stock level indicators
- [ ] Real-time pricing updates
- [ ] Social proof badges
- [ ] Recently viewed products
