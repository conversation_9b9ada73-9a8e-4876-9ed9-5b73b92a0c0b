import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  IconButton,
  Chip,
  Rating,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  CardMedia,
  Stack,
  Paper,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import './Product.css';
import productData from './productData.json';

const Product = () => {
  // Search and Filter States
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedBrand, setSelectedBrand] = useState('All');
  const [priceRange, setPriceRange] = useState([productData.priceRange.min, productData.priceRange.max]);
  const [sortBy, setSortBy] = useState('relevance');
  const [viewMode, setViewMode] = useState('grid');

  // User Interaction States
  const [favorites, setFavorites] = useState(new Set());
  const [cart, setCart] = useState(new Set());

  // Configurator States - Control what's visible in product list
  const [showConfig, setShowConfig] = useState({
    showBrand: true,
    showDescription: true,
    showRating: true,
    showVariants: true,
    showOriginalPrice: true,
    showStockBadge: true,
    showSaleBadge: true,
    showFavoriteButton: true,
    showAddToCart: true,
    showDetailsButton: true,
    compactView: false,
    showProductImages: true,
    showReviewCount: true
  });

  // Filter Functions
  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const addToCart = (productId) => {
    const newCart = new Set(cart);
    newCart.add(productId);
    setCart(newCart);
  };

  const filteredProducts = productData.products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    const matchesBrand = selectedBrand === 'All' || product.brand === selectedBrand;
    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesBrand && matchesPrice;
  });

  const handleConfigChange = (configKey) => (event) => {
    setShowConfig(prev => ({
      ...prev,
      [configKey]: event.target.checked
    }));
  };

  const resetConfiguration = () => {
    setShowConfig({
      showBrand: true,
      showDescription: true,
      showRating: true,
      showVariants: true,
      showOriginalPrice: true,
      showStockBadge: true,
      showSaleBadge: true,
      showFavoriteButton: true,
      showAddToCart: true,
      showDetailsButton: true,
      compactView: false,
      showProductImages: true,
      showReviewCount: true
    });
  };

  return (
    <Box className="product-page-container">
      <Container maxWidth="xl">
        {/* Header */}
        <Box className="product-page-header">
          <Typography variant="h3" className="product-page-title">
            Network Commerce - Product Catalog
          </Typography>

          {/* Search Section */}
          <Paper className="search-section">
            <Stack className="search-container">
              <TextField
                fullWidth
                placeholder="Search products, brands, categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: <SearchIcon className="search-icon" />
                  }
                }}
              />
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                className="search-button"
              >
                Search
              </Button>
            </Stack>
          </Paper>
        </Box>

        {/* 5-Panel Layout */}
        <Box className="five-panel-layout">
          {/* Panel 1 - Filters */}
          <Paper className="filters-panel">
            <Typography variant="h6" className="filters-title">
              Filters
            </Typography>

            {/* Category Filter */}
            <Box className="filter-group">
              <Typography className="filter-label">Category</Typography>
              <FormControl fullWidth size="small">
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  {productData.categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {/* Brand Filter */}
            <Box className="filter-group">
              <Typography className="filter-label">Brand</Typography>
              <Box className="filter-chips-container">
                {productData.brands.slice(0, 8).map((brand) => (
                  <Chip
                    key={brand}
                    label={brand}
                    variant="outlined"
                    clickable
                    size="small"
                    className={`filter-chip ${selectedBrand === brand ? 'filter-chip-active' : ''}`}
                    onClick={() => setSelectedBrand(selectedBrand === brand ? 'All' : brand)}
                  />
                ))}
              </Box>
            </Box>

            {/* Price Range */}
            <Box className="filter-group">
              <Typography className="filter-label">Price Range</Typography>
              <Box className="price-range-container">
                <Slider
                  value={priceRange}
                  onChange={(_, newValue) => setPriceRange(newValue)}
                  valueLabelDisplay="auto"
                  min={productData.priceRange.min}
                  max={productData.priceRange.max}
                  marks={productData.priceRange.marks}
                />
                <Typography className="price-range-text">
                  ${priceRange[0]} - ${priceRange[1]}
                </Typography>
              </Box>
            </Box>

            {/* Quick Filters */}
            <Box className="filter-group">
              <Typography className="filter-label">Quick Filters</Typography>
              <Box className="filter-chips-container">
                {productData.quickFilters.map((filter) => (
                  <Chip
                    key={filter.id}
                    label={filter.label}
                    variant="outlined"
                    clickable
                    size="small"
                    className="filter-chip"
                  />
                ))}
              </Box>
            </Box>
          </Paper>

          {/* Panel 2 - Configuration */}
          <Paper className="configurator-panel">
            <Typography variant="h6" className="configurator-title">
              <SettingsIcon /> Display Config
            </Typography>

            {/* Product Info Controls */}
            <Box className="config-section">
              <Typography className="config-label">Product Info</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Brand</Typography>
                <Switch
                  checked={showConfig.showBrand}
                  onChange={handleConfigChange('showBrand')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Description</Typography>
                <Switch
                  checked={showConfig.showDescription}
                  onChange={handleConfigChange('showDescription')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Images</Typography>
                <Switch
                  checked={showConfig.showProductImages}
                  onChange={handleConfigChange('showProductImages')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Variants</Typography>
                <Switch
                  checked={showConfig.showVariants}
                  onChange={handleConfigChange('showVariants')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Display Controls */}
            <Box className="config-section">
              <Typography className="config-label">Display</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Rating</Typography>
                <Switch
                  checked={showConfig.showRating}
                  onChange={handleConfigChange('showRating')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Badges</Typography>
                <Switch
                  checked={showConfig.showStockBadge}
                  onChange={handleConfigChange('showStockBadge')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Compact</Typography>
                <Switch
                  checked={showConfig.compactView}
                  onChange={handleConfigChange('compactView')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Actions */}
            <Box className="config-actions">
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                className="config-button"
                fullWidth
              >
                Save
              </Button>

              <Button
                variant="outlined"
                size="small"
                startIcon={<RefreshIcon />}
                onClick={resetConfiguration}
                className="config-button-secondary"
                fullWidth
              >
                Reset
              </Button>
            </Box>
          </Paper>

          {/* Panel 3 - Featured Products */}
          <Paper className="product-column">
            <Box className="product-column-header">
              <Typography className="column-title">Featured Products</Typography>
              <Typography className="column-count">
                {filteredProducts.filter(p => p.isFeatured).length}
              </Typography>
            </Box>

            {filteredProducts
              .filter(product => product.isFeatured)
              .slice(0, 6)
              .map((product) => (
                <Card
                  key={product.id}
                  className={`product-card ${showConfig.compactView ? 'product-card-compact' : ''} ${product.isFeatured ? 'product-card-featured' : ''}`}
                >
                  {showConfig.showProductImages && (
                    <Box className="product-image-container">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="product-image"
                        style={{ height: showConfig.compactView ? '120px' : '160px' }}
                      />

                      <Box className="product-badges">
                        {showConfig.showStockBadge && (
                          <span className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}>
                            {product.inStock ? 'In Stock' : 'Out of Stock'}
                          </span>
                        )}
                        {showConfig.showSaleBadge && product.originalPrice > product.price && (
                          <span className="stock-badge sale-badge">
                            -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                          </span>
                        )}
                      </Box>

                      {showConfig.showFavoriteButton && (
                        <button
                          className="favorite-button"
                          onClick={() => toggleFavorite(product.id)}
                        >
                          {favorites.has(product.id) ? (
                            <FavoriteIcon className="favorite-active" />
                          ) : (
                            <FavoriteBorderIcon className="favorite-inactive" />
                          )}
                        </button>
                      )}
                    </Box>
                  )}

                  <CardContent className="product-content">
                    {showConfig.showBrand && (
                      <Typography className="product-brand">
                        {product.brand}
                      </Typography>
                    )}

                    <Typography variant="h6" className="product-name">
                      {product.name}
                    </Typography>

                    {showConfig.showDescription && !showConfig.compactView && (
                      <Typography className="product-description">
                        {product.description}
                      </Typography>
                    )}

                    {showConfig.showRating && (
                      <Box className="product-rating">
                        <Rating value={product.rating} precision={0.1} readOnly size="small" />
                        {showConfig.showReviewCount && (
                          <Typography className="rating-text">
                            ({product.reviews})
                          </Typography>
                        )}
                      </Box>
                    )}

                    <Box className="product-pricing">
                      <Typography className="price-current">
                        ${product.price}
                      </Typography>
                      {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                        <Typography className="price-original">
                          ${product.originalPrice}
                        </Typography>
                      )}
                    </Box>

                    {!showConfig.compactView && (
                      <Box className="product-actions">
                        {showConfig.showAddToCart && (
                          <Button
                            variant="contained"
                            startIcon={<CartIcon />}
                            onClick={() => addToCart(product.id)}
                            disabled={!product.inStock}
                            className="add-cart-button"
                            size="small"
                            fullWidth
                          >
                            {cart.has(product.id) ? 'Added' : 'Add'}
                          </Button>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              ))}
          </Paper>

          {/* Panel 4 - Best Sellers */}
          <Paper className="product-column">
            <Box className="product-column-header">
              <Typography className="column-title">Best Sellers</Typography>
              <Typography className="column-count">
                {filteredProducts.filter(p => p.rating >= 4.5).length}
              </Typography>
            </Box>

            {filteredProducts
              .filter(product => product.rating >= 4.5)
              .sort((a, b) => b.reviews - a.reviews)
              .slice(0, 8)
              .map((product) => (
                <Card
                  key={product.id}
                  className={`product-card ${showConfig.compactView ? 'product-card-compact' : ''}`}
                >
                  {showConfig.showProductImages && (
                    <Box className="product-image-container">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="product-image"
                        style={{ height: showConfig.compactView ? '120px' : '160px' }}
                      />

                      <Box className="product-badges">
                        {showConfig.showStockBadge && (
                          <span className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}>
                            {product.inStock ? 'In Stock' : 'Out of Stock'}
                          </span>
                        )}
                        {showConfig.showSaleBadge && product.originalPrice > product.price && (
                          <span className="stock-badge sale-badge">
                            -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                          </span>
                        )}
                      </Box>

                      {showConfig.showFavoriteButton && (
                        <button
                          className="favorite-button"
                          onClick={() => toggleFavorite(product.id)}
                        >
                          {favorites.has(product.id) ? (
                            <FavoriteIcon className="favorite-active" />
                          ) : (
                            <FavoriteBorderIcon className="favorite-inactive" />
                          )}
                        </button>
                      )}
                    </Box>
                  )}

                  <CardContent className="product-content">
                    {showConfig.showBrand && (
                      <Typography className="product-brand">
                        {product.brand}
                      </Typography>
                    )}

                    <Typography variant="h6" className="product-name">
                      {product.name}
                    </Typography>

                    {showConfig.showDescription && !showConfig.compactView && (
                      <Typography className="product-description">
                        {product.description}
                      </Typography>
                    )}

                    {showConfig.showRating && (
                      <Box className="product-rating">
                        <Rating value={product.rating} precision={0.1} readOnly size="small" />
                        {showConfig.showReviewCount && (
                          <Typography className="rating-text">
                            ({product.reviews})
                          </Typography>
                        )}
                      </Box>
                    )}

                    <Box className="product-pricing">
                      <Typography className="price-current">
                        ${product.price}
                      </Typography>
                      {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                        <Typography className="price-original">
                          ${product.originalPrice}
                        </Typography>
                      )}
                    </Box>

                    {!showConfig.compactView && (
                      <Box className="product-actions">
                        {showConfig.showAddToCart && (
                          <Button
                            variant="contained"
                            startIcon={<CartIcon />}
                            onClick={() => addToCart(product.id)}
                            disabled={!product.inStock}
                            className="add-cart-button"
                            size="small"
                            fullWidth
                          >
                            {cart.has(product.id) ? 'Added' : 'Add'}
                          </Button>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              ))}
          </Paper>

          {/* Panel 5 - New Arrivals */}
          <Paper className="product-column">
            <Box className="product-column-header">
              <Typography className="column-title">New Arrivals</Typography>
              <Typography className="column-count">
                {filteredProducts.filter(p => p.isOnSale).length}
              </Typography>
            </Box>

            {filteredProducts
              .filter(product => product.isOnSale)
              .sort((a, b) => b.priority - a.priority)
              .slice(0, 10)
              .map((product) => (
                <Card
                  key={product.id}
                  className={`product-card ${showConfig.compactView ? 'product-card-compact' : ''} ${product.isOnSale ? 'product-card-sale' : ''}`}
                >
                  {showConfig.showProductImages && (
                    <Box className="product-image-container">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="product-image"
                        style={{ height: showConfig.compactView ? '120px' : '160px' }}
                      />

                      <Box className="product-badges">
                        {showConfig.showStockBadge && (
                          <span className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}>
                            {product.inStock ? 'In Stock' : 'Out of Stock'}
                          </span>
                        )}
                        {showConfig.showSaleBadge && product.originalPrice > product.price && (
                          <span className="stock-badge sale-badge">
                            -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                          </span>
                        )}
                      </Box>

                      {showConfig.showFavoriteButton && (
                        <button
                          className="favorite-button"
                          onClick={() => toggleFavorite(product.id)}
                        >
                          {favorites.has(product.id) ? (
                            <FavoriteIcon className="favorite-active" />
                          ) : (
                            <FavoriteBorderIcon className="favorite-inactive" />
                          )}
                        </button>
                      )}
                    </Box>
                  )}

                  <CardContent className="product-content">
                    {showConfig.showBrand && (
                      <Typography className="product-brand">
                        {product.brand}
                      </Typography>
                    )}

                    <Typography variant="h6" className="product-name">
                      {product.name}
                    </Typography>

                    {showConfig.showDescription && !showConfig.compactView && (
                      <Typography className="product-description">
                        {product.description}
                      </Typography>
                    )}

                    {showConfig.showRating && (
                      <Box className="product-rating">
                        <Rating value={product.rating} precision={0.1} readOnly size="small" />
                        {showConfig.showReviewCount && (
                          <Typography className="rating-text">
                            ({product.reviews})
                          </Typography>
                        )}
                      </Box>
                    )}

                    <Box className="product-pricing">
                      <Typography className="price-current">
                        ${product.price}
                      </Typography>
                      {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                        <Typography className="price-original">
                          ${product.originalPrice}
                        </Typography>
                      )}
                    </Box>

                    {!showConfig.compactView && (
                      <Box className="product-actions">
                        {showConfig.showAddToCart && (
                          <Button
                            variant="contained"
                            startIcon={<CartIcon />}
                            onClick={() => addToCart(product.id)}
                            disabled={!product.inStock}
                            className="add-cart-button"
                            size="small"
                            fullWidth
                          >
                            {cart.has(product.id) ? 'Added' : 'Add'}
                          </Button>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              ))}
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;

                    {/* Product Content */}
                    <CardContent className="product-content">
                      {/* Brand */}
                      {showConfig.showBrand && (
                        <Typography className="product-brand">
                          {product.brand}
                        </Typography>
                      )}

                      {/* Product Name */}
                      <Typography variant="h6" className="product-name">
                        {product.name}
                      </Typography>

                      {/* Description */}
                      {showConfig.showDescription && !showConfig.compactView && (
                        <Typography className="product-description">
                          {product.description}
                        </Typography>
                      )}

                      {/* Rating */}
                      {showConfig.showRating && (
                        <Box className="product-rating">
                          <Rating value={product.rating} precision={0.1} readOnly size="small" />
                          {showConfig.showReviewCount && (
                            <Typography className="rating-text">
                              ({product.reviews})
                            </Typography>
                          )}
                        </Box>
                      )}

                      {/* Variants */}
                      {showConfig.showVariants && !showConfig.compactView && (
                        <Box className="product-variants">
                          <Typography className="variants-label">Variants:</Typography>
                          <Box className="variants-chips">
                            {product.variants.slice(0, 2).map((variant, index) => (
                              <Chip
                                key={index}
                                label={variant}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            ))}
                            {product.variants.length > 2 && (
                              <Chip
                                label={`+${product.variants.length - 2}`}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            )}
                          </Box>
                        </Box>
                      )}

                      {/* Pricing */}
                      <Box className="product-pricing">
                        <Typography className="price-current">
                          ${product.price}
                        </Typography>
                        {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                          <Typography className="price-original">
                            ${product.originalPrice}
                          </Typography>
                        )}
                      </Box>

                      {/* Actions */}
                      <Box className="product-actions">
                        {showConfig.showAddToCart && (
                          <Button
                            variant="contained"
                            startIcon={<CartIcon />}
                            onClick={() => addToCart(product.id)}
                            disabled={!product.inStock}
                            className="add-cart-button"
                            fullWidth={!showConfig.showDetailsButton}
                          >
                            {cart.has(product.id) ? 'Added' : 'Add to Cart'}
                          </Button>
                        )}

                        {showConfig.showDetailsButton && (
                          <Button
                            variant="outlined"
                            size="small"
                            className="details-button"
                          >
                            Details
                          </Button>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Box className="no-results">
                <Typography variant="h6" className="no-results-title">
                  No products found
                </Typography>
                <Typography className="no-results-text">
                  Try adjusting your search criteria or filters
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('All');
                    setSelectedBrand('All');
                    setPriceRange([productData.priceRange.min, productData.priceRange.max]);
                  }}
                  className="clear-filters-button"
                >
                  Clear All Filters
                </Button>
              </Box>
            )}
          </Paper>

          {/* Right Panel - Configurator */}
          <Paper className="configurator-panel">
            <Typography variant="h6" className="configurator-title">
              <SettingsIcon /> Display Configurator
            </Typography>

            {/* Product Information Controls */}
            <Box className="config-section">
              <Typography className="config-label">Product Information</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Brand</Typography>
                <Switch
                  checked={showConfig.showBrand}
                  onChange={handleConfigChange('showBrand')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Description</Typography>
                <Switch
                  checked={showConfig.showDescription}
                  onChange={handleConfigChange('showDescription')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Product Images</Typography>
                <Switch
                  checked={showConfig.showProductImages}
                  onChange={handleConfigChange('showProductImages')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Variants</Typography>
                <Switch
                  checked={showConfig.showVariants}
                  onChange={handleConfigChange('showVariants')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Rating & Reviews Controls */}
            <Box className="config-section">
              <Typography className="config-label">Rating & Reviews</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Rating</Typography>
                <Switch
                  checked={showConfig.showRating}
                  onChange={handleConfigChange('showRating')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Review Count</Typography>
                <Switch
                  checked={showConfig.showReviewCount}
                  onChange={handleConfigChange('showReviewCount')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Pricing Controls */}
            <Box className="config-section">
              <Typography className="config-label">Pricing Display</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Original Price</Typography>
                <Switch
                  checked={showConfig.showOriginalPrice}
                  onChange={handleConfigChange('showOriginalPrice')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Badges & Indicators */}
            <Box className="config-section">
              <Typography className="config-label">Badges & Indicators</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Stock Badge</Typography>
                <Switch
                  checked={showConfig.showStockBadge}
                  onChange={handleConfigChange('showStockBadge')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Sale Badge</Typography>
                <Switch
                  checked={showConfig.showSaleBadge}
                  onChange={handleConfigChange('showSaleBadge')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Action Buttons */}
            <Box className="config-section">
              <Typography className="config-label">Action Buttons</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Favorite Button</Typography>
                <Switch
                  checked={showConfig.showFavoriteButton}
                  onChange={handleConfigChange('showFavoriteButton')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Add to Cart</Typography>
                <Switch
                  checked={showConfig.showAddToCart}
                  onChange={handleConfigChange('showAddToCart')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Details Button</Typography>
                <Switch
                  checked={showConfig.showDetailsButton}
                  onChange={handleConfigChange('showDetailsButton')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Layout Options */}
            <Box className="config-section">
              <Typography className="config-label">Layout Options</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Compact View</Typography>
                <Switch
                  checked={showConfig.compactView}
                  onChange={handleConfigChange('compactView')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Configuration Actions */}
            <Box className="config-actions">
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                className="config-button"
                fullWidth
              >
                Save Configuration
              </Button>

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetConfiguration}
                className="config-button-secondary"
                fullWidth
              >
                Reset to Default
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;