import React from "react";
import {
  <PERSON>,
  <PERSON>rid,
  <PERSON><PERSON><PERSON>,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Divider,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Rating,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Stack,
  Badge,
  IconButton,
} from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder";
import { useTheme } from "../contexts/ThemeContext";

// Dummy data for demonstration
const product = {
  name: "ATHENA NECKLACE CASE",
  brand: "<PERSON>",
  price: 79.99,
  salePrice: 55.99,
  currency: "EUR",
  rating: 4.5,
  reviews: 31,
  colors: [
    { name: "Black", hex: "#000000" },
    { name: "White", hex: "#FFFFFF" },
    { name: "Purple", hex: "#b6a6c9" },
    { name: "<PERSON><PERSON>", hex: "#e5d7c6" },
  ],
  variants: [
    { label: "iPhone 11 Pro", value: "iphone11pro" },
    { label: "iPhone 12 Pro", value: "iphone12pro" },
  ],
  media: [
    "/images/product1.jpg",
    "/images/product2.jpg",
    "/images/product3.jpg",
    "/images/product4.jpg",
  ],
  description: `De Athena Kettinghoesje in Black is ontworpen met speelsheid, bewegelijkheid en praktisch nut in gedachten. Deze nieuwe lijn combineert op deskundige wijze antiwear met trendy kleuren, slim gebruik van kenmerkende metalen details, en modulaire functies voor ultieme draagbaarheid.`,
  details: [
    "Geschikt voor iPhone 11 Pro",
    "Verwijderbare ketting",
    "Hoogwaardige materialen",
    "Bescherming tegen krassen",
  ],
  crossSell: [
    {
      name: "Magnet Wallet++",
      price: 39.99,
      salePrice: 27.99,
      image: "/images/wallet.jpg",
    },
    {
      name: "Car Mount",
      price: 29.99,
      salePrice: 20.99,
      image: "/images/carmount.jpg",
    },
    {
      name: "Athena Buckle Bag",
      price: 99.99,
      salePrice: 69.99,
      image: "/images/bag.jpg",
    },
  ],
  recommendations: [
    {
      name: "Jona Crossbag",
      price: 149.99,
      salePrice: 104.99,
      image: "/images/crossbag.jpg",
    },
    {
      name: "Athena Bumbag",
      price: 79.99,
      salePrice: 55.99,
      image: "/images/bumbag.jpg",
    },
  ],
};

const Product = () => {
  const { getMuiPaletteConfig } = useTheme();
  const [selectedColor, setSelectedColor] = React.useState(product.colors[0].name);
  const [selectedVariant, setSelectedVariant] = React.useState(product.variants[0].value);
  const [tab, setTab] = React.useState(0);

  // MUI theme palette (if needed for sx)
  const palette = getMuiPaletteConfig();

  return (
    <Box
      sx={{
        backgroundColor: "var(--theme-background-primary)",
        minHeight: "100vh",
        py: 4,
        px: { xs: 1, md: 4 },
      }}
    >
      <Grid container spacing={4}>
        {/* Product Media */}
        <Grid item xs={12} md={5}>
          <Card
            sx={{
              boxShadow: "var(--card-shadow)",
              borderRadius: "var(--card-border-radius)",
              background: "var(--card-bg)",
            }}
          >
            <CardMedia
              component="img"
              image={product.media[0]}
              alt={product.name}
              sx={{
                width: "100%",
                height: 400,
                objectFit: "cover",
                borderRadius: "var(--card-border-radius)",
              }}
            />
            <Stack direction="row" spacing={1} sx={{ mt: 2, px: 2, pb: 2 }}>
              {product.media.map((img, idx) => (
                <Avatar
                  key={idx}
                  src={img}
                  variant="rounded"
                  sx={{
                    width: 56,
                    height: 56,
                    border: selectedColor === product.colors[idx]?.name ? "2px solid var(--color-primary-brand)" : "1px solid var(--theme-border-color)",
                    cursor: "pointer",
                  }}
                />
              ))}
            </Stack>
          </Card>
        </Grid>

        {/* Product Details */}
        <Grid item xs={12} md={7}>
          <Box>
            <Typography
              variant="h4"
              sx={{
                color: "var(--color-text-default)",
                fontWeight: "var(--font-weight-bold)",
                mb: 1,
              }}
            >
              {product.name}
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{ color: "var(--color-text-secondary)", mb: 2 }}
            >
              {product.brand}
            </Typography>
            <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
              <Rating
                value={product.rating}
                precision={0.5}
                readOnly
                sx={{
                  color: "var(--color-rating-star)",
                }}
              />
              <Typography
                variant="body2"
                sx={{ color: "var(--color-text-muted)" }}
              >
                {product.reviews} reviews
              </Typography>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
              <Typography
                variant="h5"
                sx={{
                  color: "var(--color-status-error)",
                  fontWeight: "var(--font-weight-bold)",
                }}
              >
                {product.currency} {product.salePrice.toFixed(2)}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: "var(--color-text-muted)",
                  textDecoration: "line-through",
                }}
              >
                {product.currency} {product.price.toFixed(2)}
              </Typography>
              <Chip
                label="30% OFF EVERYTHING"
                sx={{
                  background: "#39ff14",
                  color: "#000",
                  fontWeight: "var(--font-weight-bold)",
                  ml: 2,
                }}
              />
            </Stack>
            {/* Color selection */}
            <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
              {product.colors.map((color) => (
                <IconButton
                  key={color.name}
                  onClick={() => setSelectedColor(color.name)}
                  sx={{
                    border: selectedColor === color.name ? "2px solid var(--color-primary-brand)" : "1px solid var(--theme-border-color)",
                    background: color.hex,
                    width: 32,
                    height: 32,
                  }}
                />
              ))}
            </Stack>
            {/* Variant selection */}
            <FormControl sx={{ minWidth: 180, mb: 2 }}>
              <InputLabel id="variant-label">Variant</InputLabel>
              <Select
                labelId="variant-label"
                value={selectedVariant}
                label="Variant"
                onChange={(e) => setSelectedVariant(e.target.value)}
                sx={{
                  background: "var(--card-bg)",
                  color: "var(--color-text-default)",
                  ".MuiOutlinedInput-notchedOutline": {
                    borderColor: "var(--theme-border-color)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "var(--color-primary-brand)",
                  },
                }}
              >
                {product.variants.map((variant) => (
                  <MenuItem key={variant.value} value={variant.value}>
                    {variant.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {/* Add to cart and wishlist */}
            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
              <Button
                variant="contained"
                startIcon={<ShoppingCartIcon />}
                sx={{
                  background: "var(--button-primary-bg)",
                  color: "var(--button-color)",
                  fontWeight: "var(--font-weight-bold)",
                  "&:hover": {
                    background: "var(--button-primary-hover-bg)",
                  },
                }}
              >
                Voeg toe aan winkelmandje
              </Button>
              <IconButton
                sx={{
                  border: "1px solid var(--theme-border-color)",
                  color: "var(--color-primary-brand)",
                  background: "var(--card-bg)",
                  "&:hover": {
                    background: "var(--theme-background-primary)",
                  },
                }}
              >
                <FavoriteBorderIcon />
              </IconButton>
            </Stack>
            {/* Klarna payment info */}
            <Box
              sx={{
                background: "var(--theme-background-secondary)",
                border: "1px solid var(--theme-border-color)",
                borderRadius: "var(--radius-md)",
                p: 2,
                mb: 2,
              }}
            >
              <Typography
                variant="body2"
                sx={{ color: "var(--color-text-default)" }}
              >
                Betaal in 3 delen van 18,66 € <a href="#" style={{ color: "var(--color-text-link)" }}>Meer informatie</a>
              </Typography>
            </Box>
            {/* Tabs for details/reviews */}
            <Tabs
              value={tab}
              onChange={(_, v) => setTab(v)}
              sx={{
                mb: 2,
                ".MuiTabs-indicator": {
                  background: "var(--color-primary-brand)",
                },
              }}
            >
              <Tab label="Details" />
              <Tab label="Reviews" />
            </Tabs>
            {tab === 0 && (
              <Box>
                <Typography
                  variant="body1"
                  sx={{ color: "var(--color-text-default)", mb: 1 }}
                >
                  {product.description}
                </Typography>
                <List>
                  {product.details.map((detail, idx) => (
                    <ListItem key={idx} sx={{ py: 0 }}>
                      <ListItemText
                        primary={detail}
                        primaryTypographyProps={{
                          color: "var(--color-text-secondary)",
                        }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
            {tab === 1 && (
              <Box>
                <Typography
                  variant="body2"
                  sx={{ color: "var(--color-text-muted)", mb: 1 }}
                >
                  (Reviews section coming soon)
                </Typography>
                <TextField
                  label="Write a review"
                  multiline
                  rows={3}
                  fullWidth
                  sx={{
                    background: "var(--theme-background-secondary)",
                    borderRadius: "var(--radius-md)",
                    mb: 2,
                  }}
                />
                <Button
                  variant="contained"
                  sx={{
                    background: "var(--button-primary-bg)",
                    color: "var(--button-color)",
                    fontWeight: "var(--font-weight-bold)",
                    "&:hover": {
                      background: "var(--button-primary-hover-bg)",
                    },
                  }}
                >
                  Submit
                </Button>
              </Box>
            )}
          </Box>
        </Grid>
      </Grid>

      {/* Cross-sell and Recommendations */}
      <Box sx={{ mt: 6 }}>
        <Typography
          variant="h6"
          sx={{
            color: "var(--color-text-default)",
            fontWeight: "var(--font-weight-bold)",
            mb: 2,
          }}
        >
          Maak de look af
        </Typography>
        <Grid container spacing={2}>
          {product.crossSell.map((item, idx) => (
            <Grid item xs={12} sm={6} md={2.4} key={idx}>
              <Card
                sx={{
                  boxShadow: "var(--card-shadow)",
                  borderRadius: "var(--card-border-radius)",
                  background: "var(--card-bg)",
                  p: 1,
                }}
              >
                <CardMedia
                  component="img"
                  image={item.image}
                  alt={item.name}
                  sx={{
                    height: 80,
                    objectFit: "contain",
                    borderRadius: "var(--radius-sm)",
                  }}
                />
                <CardContent sx={{ p: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "var(--color-text-default)", fontWeight: "var(--font-weight-medium)" }}
                  >
                    {item.name}
                  </Typography>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Typography
                      variant="body2"
                      sx={{ color: "var(--color-status-error)", fontWeight: "var(--font-weight-bold)" }}
                    >
                      {product.currency} {item.salePrice.toFixed(2)}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "var(--color-text-muted)", textDecoration: "line-through" }}
                    >
                      {product.currency} {item.price.toFixed(2)}
                    </Typography>
                  </Stack>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{
                      mt: 1,
                      borderColor: "var(--color-primary-brand)",
                      color: "var(--color-primary-brand)",
                      fontWeight: "var(--font-weight-bold)",
                      "&:hover": {
                        borderColor: "var(--color-primary-brand)",
                        background: "var(--theme-background-primary)",
                      },
                    }}
                  >
                    + Toevoegen
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default Product;