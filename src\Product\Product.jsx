import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  IconButton,
  Chip,
  <PERSON>ing,
  FormControl,
  InputLabel,
  Select,
  MenuItem,

  Switch,
  FormControlLabel,
  Card,
  CardContent,
  CardMedia,
  Stack,
  Paper,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,

  Refresh as RefreshIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import './Product.css';
import productData from './productData.json';

const Product = () => {
  // Search and Display States
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('relevance');
  const [viewMode, setViewMode] = useState('grid');

  // User Interaction States
  const [favorites, setFavorites] = useState(new Set());
  const [cart, setCart] = useState(new Set());

  // Configurator States - Control what's visible in product list
  const [showConfig, setShowConfig] = useState({
    // Legacy display options (working)
    showBrand: true,
    showDescription: true,
    showRating: true,
    showVariants: true,
    showOriginalPrice: true,
    showStockBadge: true,
    showSaleBadge: true,
    showFavoriteButton: true,
    showAddToCart: true,
    showDetailsButton: true,
    compactView: false,
    showProductImages: true,
    showReviewCount: true,

    // 1. Basic Product Information
    productSKU: false,
    productStatus: false,
    shortDescription: true,
    longDescription: false,
    productVideos: false,
    internalProductID: false,

    // 2. Pricing & Financials
    salePrice: true,
    saleStartEndDate: false,
    taxClassStatus: false,
    costOfGoods: false,
    profitMargin: false,
    commissionRate: false,

    // 3. Inventory & Shipping
    stockQuantity: false,
    allowBackorders: false,
    lowStockThreshold: false,
    weight: false,
    dimensions: false,
    shippingClass: false,
    warehouseLocation: false,
    supplierName: false,

    // 4. Organization & Attributes
    productCategory: false,
    productTags: false,
    attributes: false,
    variations: false,

    // 5. Marketing & SEO
    seoTitle: false,
    seoMetaDescription: false,
    upsells: false,
    crossSells: false,
    featuredProduct: false,

    // 6. Analytics & Performance
    pageViews: false,
    salesCount: false,
    totalRevenue: false,
    customerReviews: false,
    reviewApprovalStatus: false,
    conversionRate: false,
    platformAnalytics: false,

    // 7. Internal & System Data
    internalNotes: false,
    userOwnerID: false,
    dateCreated: false,
    lastModifiedBy: false,
    integrationIDs: false,

    // 8-16. Other groups (for future expansion)
    geoList: false,
    geoAll: false,
    multipleSelection: false,
    skuQuantity: false,
    priceApplication: false,
    productsApplicable: false,
    allProducts: false,
    customersList: false,
    allCustomers: false,
    customerMultipleSelection: false,
    automotiveBrand: false,
    automotiveYear: false,
    automotiveMultipleSelection: false,
    automotiveAll: false,
    automotiveFiltering: false,
    quantityDiscount: false,
    quantityRate: false,
    deliveryTerms: false,
    shippingConditions: false,
    combinationOffers: false,
    orderDateValidity: false
  });

  // Filter Functions
  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const addToCart = (productId) => {
    const newCart = new Set(cart);
    newCart.add(productId);
    setCart(newCart);
  };

  const filteredProducts = productData.products.filter(product => {
    const matchesSearch = searchQuery === '' ||
                         product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  const handleConfigChange = (configKey) => (event) => {
    setShowConfig(prev => ({
      ...prev,
      [configKey]: event.target.checked
    }));
  };

  const resetConfiguration = () => {
    setShowConfig({
      showBrand: true,
      showDescription: true,
      showRating: true,
      showVariants: true,
      showOriginalPrice: true,
      showStockBadge: true,
      showSaleBadge: true,
      showFavoriteButton: true,
      showAddToCart: true,
      showDetailsButton: true,
      compactView: false,
      showProductImages: true,
      showReviewCount: true
    });
  };

  return (
    <Box className="product-page-container">
      <Container maxWidth="xl">
        {/* Header */}
       

        {/* 3-Panel Layout - Wireframe Style */}
        <Box className="three-panel-layout">
          {/* Left Panel - FILTER */}
          <Paper className="filters-panel">
            <Box className="panel-header">
              <Box>
                <Typography className="panel-title">FILTER</Typography>
                <Typography className="panel-subtitle">Filters</Typography>
              </Box>
            </Box>

            {/* Filter content area - empty for now */}
            <Box className="filter-content">
              <Typography variant="body2" className="filter-placeholder">
                Filter options will be added here
              </Typography>
            </Box>
          </Paper>

          {/* Center Panel - Product */}
          <Paper className="products-main-panel">
            

            {/* Main Products Grid */}
            {filteredProducts.length > 0 ? (
              <Box className={viewMode === 'grid' ? 'products-grid' : 'products-list'}>
                {filteredProducts.map((product) => (
                  <Card
                    key={product.id}
                    className={`product-card ${showConfig.compactView ? 'product-card-compact' : ''} ${product.isFeatured ? 'product-card-featured' : ''} ${product.isOnSale ? 'product-card-sale' : ''}`}
                  >
                    {showConfig.showProductImages && (
                      <Box className="product-image-container">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="product-image"
                          style={{ height: showConfig.compactView ? '150px' : '200px' }}
                        />

                        <Box className="product-badges">
                          {showConfig.showStockBadge && (
                            <span className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}>
                              {product.inStock ? 'In Stock' : 'Out of Stock'}
                            </span>
                          )}
                          {showConfig.showSaleBadge && product.originalPrice > product.price && (
                            <span className="stock-badge sale-badge">
                              -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                            </span>
                          )}
                        </Box>

                        {showConfig.showFavoriteButton && (
                          <button
                            className="favorite-button"
                            onClick={() => toggleFavorite(product.id)}
                          >
                            {favorites.has(product.id) ? (
                              <FavoriteIcon className="favorite-active" />
                            ) : (
                              <FavoriteBorderIcon className="favorite-inactive" />
                            )}
                          </button>
                        )}
                      </Box>
                    )}

                    <CardContent className="product-content">
                      {/* Product SKU */}
                      {showConfig.productSKU && (
                        <Typography className="product-sku" variant="caption">
                          SKU: {product.id.toString().padStart(6, '0')}
                        </Typography>
                      )}

                      {/* Product Status */}
                      {showConfig.productStatus && (
                        <Typography className="product-status" variant="caption">
                          Status: {product.inStock ? 'Active' : 'Inactive'}
                        </Typography>
                      )}

                      {/* Brand */}
                      {showConfig.showBrand && (
                        <Typography className="product-brand">
                          {product.brand}
                        </Typography>
                      )}

                      {/* Product Name */}
                      <Typography variant="h6" className="product-name">
                        {product.name}
                      </Typography>

                      {/* Short Description */}
                      {showConfig.shortDescription && !showConfig.compactView && (
                        <Typography className="product-short-description" variant="body2">
                          {product.description.substring(0, 80)}...
                        </Typography>
                      )}

                      {/* Long Description */}
                      {showConfig.longDescription && !showConfig.compactView && (
                        <Typography className="product-long-description" variant="body2">
                          {product.description}
                        </Typography>
                      )}

                      {/* Legacy Description (for backward compatibility) */}
                      {showConfig.showDescription && !showConfig.shortDescription && !showConfig.longDescription && !showConfig.compactView && (
                        <Typography className="product-description">
                          {product.description}
                        </Typography>
                      )}

                      {/* Internal Product ID */}
                      {showConfig.internalProductID && (
                        <Typography className="product-internal-id" variant="caption">
                          Internal ID: INT-{product.id}-{product.brand.substring(0, 3).toUpperCase()}
                        </Typography>
                      )}

                      {showConfig.showRating && (
                        <Box className="product-rating">
                          <Rating value={product.rating} precision={0.1} readOnly size="small" />
                          {showConfig.showReviewCount && (
                            <Typography className="rating-text">
                              ({product.reviews})
                            </Typography>
                          )}
                        </Box>
                      )}

                      {showConfig.showVariants && !showConfig.compactView && (
                        <Box className="product-variants">
                          <Typography className="variants-label">Variants:</Typography>
                          <Box className="variants-chips">
                            {product.variants.slice(0, 3).map((variant, index) => (
                              <Chip
                                key={index}
                                label={variant}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            ))}
                            {product.variants.length > 3 && (
                              <Chip
                                label={`+${product.variants.length - 3}`}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            )}
                          </Box>
                        </Box>
                      )}

                      {/* Inventory & Shipping Information */}
                      <Box className="inventory-info">
                        {/* Stock Quantity */}
                        {showConfig.stockQuantity && (
                          <Typography className="stock-quantity" variant="caption">
                            Stock: {Math.floor(Math.random() * 100) + 10} units
                          </Typography>
                        )}

                        {/* Allow Backorders */}
                        {showConfig.allowBackorders && (
                          <Typography className="backorders-status" variant="caption">
                            Backorders: {Math.random() > 0.5 ? 'Allowed' : 'Not Allowed'}
                          </Typography>
                        )}

                        {/* Low Stock Threshold */}
                        {showConfig.lowStockThreshold && (
                          <Typography className="low-stock-threshold" variant="caption">
                            Low Stock Alert: 5 units
                          </Typography>
                        )}

                        {/* Weight */}
                        {showConfig.weight && (
                          <Typography className="product-weight" variant="caption">
                            Weight: {(Math.random() * 5 + 0.5).toFixed(2)} kg
                          </Typography>
                        )}

                        {/* Dimensions */}
                        {showConfig.dimensions && (
                          <Typography className="product-dimensions" variant="caption">
                            Dimensions: {Math.floor(Math.random() * 20 + 10)}×{Math.floor(Math.random() * 15 + 8)}×{Math.floor(Math.random() * 10 + 3)} cm
                          </Typography>
                        )}
                      </Box>

                      <Box className="product-pricing">
                        {/* Sale Price */}
                        {showConfig.salePrice && (
                          <Typography className="price-current">
                            ${product.price}
                          </Typography>
                        )}

                        {/* Original Price */}
                        {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                          <Typography className="price-original">
                            ${product.originalPrice}
                          </Typography>
                        )}

                        {/* Sale Start/End Date */}
                        {showConfig.saleStartEndDate && product.originalPrice > product.price && (
                          <Typography className="sale-dates" variant="caption">
                            Sale: Dec 1 - Dec 31, 2024
                          </Typography>
                        )}

                        {/* Tax Class/Status */}
                        {showConfig.taxClassStatus && (
                          <Typography className="tax-status" variant="caption">
                            Tax: Standard (8.5%)
                          </Typography>
                        )}

                        {/* Cost of Goods */}
                        {showConfig.costOfGoods && (
                          <Typography className="cost-of-goods" variant="caption">
                            COG: ${(product.price * 0.6).toFixed(2)}
                          </Typography>
                        )}

                        {/* Profit Margin */}
                        {showConfig.profitMargin && (
                          <Typography className="profit-margin" variant="caption">
                            Margin: {((product.price - (product.price * 0.6)) / product.price * 100).toFixed(1)}%
                          </Typography>
                        )}

                        {/* Commission Rate */}
                        {showConfig.commissionRate && (
                          <Typography className="commission-rate" variant="caption">
                            Commission: 5.0%
                          </Typography>
                        )}
                      </Box>

                      {!showConfig.compactView && (
                        <Box className="product-actions">
                          {showConfig.showAddToCart && (
                            <Button
                              variant="contained"
                              startIcon={<CartIcon />}
                              onClick={() => addToCart(product.id)}
                              disabled={!product.inStock}
                              className="add-cart-button"
                              fullWidth={!showConfig.showDetailsButton}
                            >
                              {cart.has(product.id) ? 'Added' : 'Add to Cart'}
                            </Button>
                          )}

                          {showConfig.showDetailsButton && (
                            <Button
                              variant="outlined"
                              size="small"
                              className="details-button"
                            >
                              Details
                            </Button>
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Box className="no-results">
                <Typography variant="h6" className="no-results-title">
                  No products found
                </Typography>
                <Typography className="no-results-text">
                  Try adjusting your search criteria or filters
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => {
                    setSearchQuery('');
                  }}
                  className="clear-filters-button"
                >
                  Clear Search
                </Button>
              </Box>
            )}
          </Paper>

          {/* Right Panel - Configurator */}
          <Paper className="configurator-panel">
            <Box className="panel-header">
              <Box>
                <Typography className="panel-title">Configurator</Typography>
                <Typography className="panel-subtitle">Product Management Settings</Typography>

                {/* Validation Counter */}
                <Box className="validation-counter">
                  <Typography variant="caption" className="validation-text">
                    Active Options: {Object.values(showConfig).filter(Boolean).length} / {Object.keys(showConfig).length}
                  </Typography>
                  <Typography variant="caption" className="validation-hint">
                    Toggle switches to see changes in product cards
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box className="config-content">
              {/* 1. Basic Product Information */}
              <Box className="config-group">
                <Typography className="config-group-title">1. Basic Product Information</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Product SKU</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.productSKU}
                      onChange={handleConfigChange('productSKU')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Status</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.productStatus}
                      onChange={handleConfigChange('productStatus')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Short Description</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.shortDescription}
                      onChange={handleConfigChange('shortDescription')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Long Description</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.longDescription}
                      onChange={handleConfigChange('longDescription')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Images</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.showProductImages}
                      onChange={handleConfigChange('showProductImages')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Videos</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.productVideos}
                      onChange={handleConfigChange('productVideos')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Internal Product ID</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.internalProductID}
                      onChange={handleConfigChange('internalProductID')}
                    />
                  </Box>
                </Box>
              </Box>

              {/* 2. Pricing & Financials */}
              <Box className="config-group">
                <Typography className="config-group-title">2. Pricing & Financials</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Sale Price</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.salePrice}
                      onChange={handleConfigChange('salePrice')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Sale Start/End Date</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.saleStartEndDate}
                      onChange={handleConfigChange('saleStartEndDate')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Tax Class/Status</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.taxClassStatus}
                      onChange={handleConfigChange('taxClassStatus')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Cost of Goods (COG)</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.costOfGoods}
                      onChange={handleConfigChange('costOfGoods')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Profit Margin</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.profitMargin}
                      onChange={handleConfigChange('profitMargin')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Commission Rate / Fee</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.commissionRate}
                      onChange={handleConfigChange('commissionRate')}
                    />
                  </Box>
                </Box>
              </Box>

              {/* 3. Inventory & Shipping */}
              <Box className="config-group">
                <Typography className="config-group-title">3. Inventory & Shipping</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Stock Quantity</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.stockQuantity}
                      onChange={handleConfigChange('stockQuantity')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Allow Backorders?</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.allowBackorders}
                      onChange={handleConfigChange('allowBackorders')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Low Stock Threshold</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.lowStockThreshold}
                      onChange={handleConfigChange('lowStockThreshold')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Weight (for shipping)</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.weight}
                      onChange={handleConfigChange('weight')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Dimensions (L/W/H)</Typography>
                    <Switch
                      size="small"
                      checked={showConfig.dimensions}
                      onChange={handleConfigChange('dimensions')}
                    />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Shipping Class</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Warehouse/Location</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Supplier Name</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 4. Organization & Attributes */}
              <Box className="config-group">
                <Typography className="config-group-title">4. Organization & Attributes</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Category</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Tags / Keywords</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Attributes (Color, Size)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Variations (and SKUs)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Brand</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 5. Marketing & SEO */}
              <Box className="config-group">
                <Typography className="config-group-title">5. Marketing & SEO</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">SEO Title</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">SEO Meta Description</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Upsells</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Cross-sells</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Featured Product?</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 6. Analytics & Performance */}
              <Box className="config-group">
                <Typography className="config-group-title">6. Analytics & Performance</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Page Views / Visits</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Sales Count (Units Sold)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Total Revenue</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Customer Reviews & Ratings</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Review Approval Status</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Conversion Rate</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Platform-wide Analytics</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 7. Internal & System Data */}
              <Box className="config-group">
                <Typography className="config-group-title">7. Internal & System Data</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Internal Notes</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">User/Owner ID</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Date Created</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Last Modified By (Admin)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Integration-specific IDs</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 8. Geo Applicability */}
              <Box className="config-group">
                <Typography className="config-group-title">8. Geo Applicability</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of Geo (pincode)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All (pincode)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Multiple selection criteria</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 9. Quantity Based */}
              <Box className="config-group">
                <Typography className="config-group-title">9. Quantity Based</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">SKU & Quantity</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Price Application</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 10. SKU Selection */}
              <Box className="config-group">
                <Typography className="config-group-title">10. SKU Selection</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of Products applicable</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 11. Customers */}
              <Box className="config-group">
                <Typography className="config-group-title">11. Customers</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of customers</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Multiple Selection</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 12. Assets (Automotive) */}
              <Box className="config-group">
                <Typography className="config-group-title">12. Assets (Automotive)</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Brand, Product type, model</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Year, serial#'s</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Multiple Selection</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Filtering</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 13. Discount Applicable */}
              <Box className="config-group">
                <Typography className="config-group-title">13. Discount Applicable</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Quantity & Discount</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Quantity and Rate</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 14. Delivery Conditions */}
              <Box className="config-group">
                <Typography className="config-group-title">14. Delivery Conditions</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Delivery Terms</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Shipping Conditions</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 15. Combination Offers */}
              <Box className="config-group">
                <Typography className="config-group-title">15. Combination Offers</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of SKU (Quantity, Rate)</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 16. Order Date Validity */}
              <Box className="config-group">
                <Typography className="config-group-title">16. Order Date Validity</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">From & To</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>
            </Box>

            {/* Configuration Actions */}
            <Box className="config-actions">
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                className="config-button"
                fullWidth
              >
                Save Configuration
              </Button>

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetConfiguration}
                className="config-button-secondary"
                fullWidth
              >
                Reset to Default
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;