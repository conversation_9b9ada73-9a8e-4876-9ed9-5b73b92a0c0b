import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  TextField,
  Button,
  IconButton,
  Chip,
  Rating,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  Avatar,
  Divider,
  Stack,
  Paper
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Star as StarIcon,
  ExpandMore as ExpandMoreIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
  Sort as SortIcon
} from '@mui/icons-material';

// Mock product data for aftermarket network commerce
const mockProducts = [
  {
    id: 1,
    name: 'Air Filter Premium',
    brand: 'AutoParts Pro',
    price: 45.99,
    originalPrice: 59.99,
    rating: 4.5,
    reviews: 128,
    image: '/api/placeholder/300/300',
    category: 'Filters',
    inStock: true,
    variants: ['Standard', 'Heavy Duty', 'Performance'],
    colors: ['Black', 'Silver'],
    description: 'High-performance air filter for enhanced engine efficiency'
  },
  {
    id: 2,
    name: 'Brake Pads Set',
    brand: 'StopTech',
    price: 89.99,
    originalPrice: 109.99,
    rating: 4.8,
    reviews: 256,
    image: '/api/placeholder/300/300',
    category: 'Brakes',
    inStock: true,
    variants: ['Ceramic', 'Semi-Metallic', 'Organic'],
    colors: ['Black'],
    description: 'Premium brake pads for superior stopping power'
  },
  {
    id: 3,
    name: 'Engine Oil 5W-30',
    brand: 'Mobil 1',
    price: 34.99,
    originalPrice: 44.99,
    rating: 4.7,
    reviews: 89,
    image: '/api/placeholder/300/300',
    category: 'Fluids',
    inStock: false,
    variants: ['1L', '4L', '5L'],
    colors: ['Amber'],
    description: 'Synthetic motor oil for maximum engine protection'
  },
  {
    id: 4,
    name: 'Spark Plugs Set',
    brand: 'NGK',
    price: 24.99,
    originalPrice: 29.99,
    rating: 4.6,
    reviews: 167,
    image: '/api/placeholder/300/300',
    category: 'Ignition',
    inStock: true,
    variants: ['Copper', 'Platinum', 'Iridium'],
    colors: ['Silver'],
    description: 'High-quality spark plugs for optimal ignition'
  },
  {
    id: 5,
    name: 'Transmission Fluid',
    brand: 'Valvoline',
    price: 19.99,
    originalPrice: 24.99,
    rating: 4.4,
    reviews: 94,
    image: '/api/placeholder/300/300',
    category: 'Fluids',
    inStock: true,
    variants: ['ATF', 'CVT', 'Manual'],
    colors: ['Red'],
    description: 'Premium transmission fluid for smooth shifting'
  },
  {
    id: 6,
    name: 'Cabin Air Filter',
    brand: 'FRAM',
    price: 15.99,
    originalPrice: 19.99,
    rating: 4.3,
    reviews: 73,
    image: '/api/placeholder/300/300',
    category: 'Filters',
    inStock: true,
    variants: ['Standard', 'Activated Carbon'],
    colors: ['White'],
    description: 'Clean air filter for improved cabin air quality'
  }
];

const categories = ['All', 'Filters', 'Brakes', 'Fluids', 'Ignition', 'Engine', 'Transmission'];
const brands = ['All', 'AutoParts Pro', 'StopTech', 'Mobil 1', 'NGK', 'Valvoline', 'FRAM'];
const sortOptions = [
  { value: 'relevance', label: 'Best Match' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Customer Rating' },
  { value: 'newest', label: 'Newest First' }
];

const Product = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedBrand, setSelectedBrand] = useState('All');
  const [priceRange, setPriceRange] = useState([0, 200]);
  const [sortBy, setSortBy] = useState('relevance');
  const [viewMode, setViewMode] = useState('grid');
  const [favorites, setFavorites] = useState(new Set());
  const [cart, setCart] = useState(new Set());

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const addToCart = (productId) => {
    const newCart = new Set(cart);
    newCart.add(productId);
    setCart(newCart);
  };

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    const matchesBrand = selectedBrand === 'All' || product.brand === selectedBrand;
    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesBrand && matchesPrice;
  });

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: 'var(--theme-background-primary)',
      color: 'var(--color-text-default)'
    }}>
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Header Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" sx={{
            mb: 2,
            color: 'var(--color-text-heading)',
            fontWeight: 'var(--font-weight-bold)'
          }}>
            Product Search
          </Typography>

          {/* Search Bar */}
          <Paper sx={{
            p: 2,
            mb: 3,
            backgroundColor: 'var(--card-bg)',
            boxShadow: 'var(--card-shadow)'
          }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <TextField
                fullWidth
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'var(--color-text-muted)' }} />
                }}
              />
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                sx={{
                  backgroundColor: 'var(--button-primary-bg)',
                  '&:hover': {
                    backgroundColor: 'var(--button-primary-hover-bg)'
                  }
                }}
              >
                Search
              </Button>
            </Stack>
          </Paper>
        </Box>

        {/* Main Content Grid */}
        <Grid container spacing={3}>
          {/* Filters Sidebar */}
          <Grid item xs={12} md={3}>
            <Paper sx={{
              p: 3,
              backgroundColor: 'var(--card-bg)',
              boxShadow: 'var(--card-shadow)',
              borderRadius: 'var(--card-border-radius)'
            }}>
              <Typography variant="h6" sx={{
                mb: 3,
                color: 'var(--color-text-heading)',
                fontWeight: 'var(--font-weight-medium)'
              }}>
                Filters
              </Typography>

              {/* Category Filter */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{
                  mb: 2,
                  color: 'var(--color-text-default)',
                  fontWeight: 'var(--font-weight-medium)'
                }}>
                  Category
                </Typography>
                <FormControl fullWidth>
                  <Select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              {/* Brand Filter */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{
                  mb: 2,
                  color: 'var(--color-text-default)',
                  fontWeight: 'var(--font-weight-medium)'
                }}>
                  Brand
                </Typography>
                <FormControl fullWidth>
                  <Select
                    value={selectedBrand}
                    onChange={(e) => setSelectedBrand(e.target.value)}
                  >
                    {brands.map((brand) => (
                      <MenuItem key={brand} value={brand}>
                        {brand}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              {/* Price Range Filter */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{
                  mb: 2,
                  color: 'var(--color-text-default)',
                  fontWeight: 'var(--font-weight-medium)'
                }}>
                  Price Range
                </Typography>
                <Slider
                  value={priceRange}
                  onChange={(e, newValue) => setPriceRange(newValue)}
                  valueLabelDisplay="auto"
                  min={0}
                  max={200}
                  marks={[
                    { value: 0, label: '$0' },
                    { value: 50, label: '$50' },
                    { value: 100, label: '$100' },
                    { value: 150, label: '$150' },
                    { value: 200, label: '$200+' }
                  ]}
                />
                <Typography variant="body2" sx={{
                  mt: 1,
                  color: 'var(--color-text-secondary)'
                }}>
                  ${priceRange[0]} - ${priceRange[1]}
                </Typography>
              </Box>

              {/* Quick Filters */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{
                  mb: 2,
                  color: 'var(--color-text-default)',
                  fontWeight: 'var(--font-weight-medium)'
                }}>
                  Quick Filters
                </Typography>
                <Stack spacing={1}>
                  <Chip
                    label="In Stock"
                    variant="outlined"
                    clickable
                    sx={{
                      borderColor: 'var(--color-primary-brand)',
                      color: 'var(--color-primary-brand)',
                      '&:hover': {
                        backgroundColor: 'var(--color-primary-brand)',
                        color: 'var(--color-white)'
                      }
                    }}
                  />
                  <Chip
                    label="On Sale"
                    variant="outlined"
                    clickable
                    sx={{
                      borderColor: 'var(--color-status-error)',
                      color: 'var(--color-status-error)',
                      '&:hover': {
                        backgroundColor: 'var(--color-status-error)',
                        color: 'var(--color-white)'
                      }
                    }}
                  />
                  <Chip
                    label="Top Rated"
                    variant="outlined"
                    clickable
                    sx={{
                      borderColor: 'var(--color-rating-star)',
                      color: 'var(--color-rating-star)',
                      '&:hover': {
                        backgroundColor: 'var(--color-rating-star)',
                        color: 'var(--color-white)'
                      }
                    }}
                  />
                </Stack>
              </Box>
            </Paper>
          </Grid>

          {/* Products Section */}
          <Grid item xs={12} md={9}>
            {/* Toolbar */}
            <Paper sx={{
              p: 2,
              mb: 3,
              backgroundColor: 'var(--card-bg)',
              boxShadow: 'var(--card-shadow)'
            }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="body1" sx={{ color: 'var(--color-text-default)' }}>
                  {filteredProducts.length} products found
                </Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                  {/* Sort Dropdown */}
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel>Sort by</InputLabel>
                    <Select
                      value={sortBy}
                      label="Sort by"
                      onChange={(e) => setSortBy(e.target.value)}
                    >
                      {sortOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  {/* View Mode Toggle */}
                  <Stack direction="row">
                    <IconButton
                      onClick={() => setViewMode('grid')}
                      sx={{
                        color: viewMode === 'grid' ? 'var(--color-primary-brand)' : 'var(--color-text-muted)'
                      }}
                    >
                      <ViewModuleIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => setViewMode('list')}
                      sx={{
                        color: viewMode === 'list' ? 'var(--color-primary-brand)' : 'var(--color-text-muted)'
                      }}
                    >
                      <ViewListIcon />
                    </IconButton>
                  </Stack>
                </Stack>
              </Stack>
            </Paper>

            {/* Products Grid */}
            <Grid container spacing={3}>
              {filteredProducts.map((product) => (
                <Grid
                  item
                  xs={12}
                  sm={viewMode === 'grid' ? 6 : 12}
                  md={viewMode === 'grid' ? 4 : 12}
                  lg={viewMode === 'grid' ? 3 : 12}
                  key={product.id}
                >
                  <Card sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: viewMode === 'list' ? 'row' : 'column',
                    backgroundColor: 'var(--card-bg)',
                    boxShadow: 'var(--card-shadow)',
                    borderRadius: 'var(--card-border-radius)',
                    transition: 'var(--transition-ease-in-out)',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                    }
                  }}>
                    {/* Product Image */}
                    <Box sx={{
                      position: 'relative',
                      width: viewMode === 'list' ? 200 : '100%',
                      height: viewMode === 'list' ? 150 : 200,
                      flexShrink: 0
                    }}>
                      <CardMedia
                        component="img"
                        height={viewMode === 'list' ? 150 : 200}
                        image={product.image}
                        alt={product.name}
                        sx={{
                          objectFit: 'cover',
                          backgroundColor: 'var(--theme-background-secondary)'
                        }}
                      />

                      {/* Stock Badge */}
                      <Chip
                        label={product.inStock ? 'In Stock' : 'Out of Stock'}
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 8,
                          left: 8,
                          backgroundColor: product.inStock ? 'var(--color-status-success)' : 'var(--color-status-error)',
                          color: 'var(--color-white)',
                          fontWeight: 'var(--font-weight-medium)'
                        }}
                      />

                      {/* Favorite Button */}
                      <IconButton
                        onClick={() => toggleFavorite(product.id)}
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 1)'
                          }
                        }}
                      >
                        {favorites.has(product.id) ? (
                          <FavoriteIcon sx={{ color: 'var(--color-status-error)' }} />
                        ) : (
                          <FavoriteBorderIcon sx={{ color: 'var(--color-text-muted)' }} />
                        )}
                      </IconButton>

                      {/* Sale Badge */}
                      {product.originalPrice > product.price && (
                        <Chip
                          label={`-${Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%`}
                          size="small"
                          sx={{
                            position: 'absolute',
                            bottom: 8,
                            left: 8,
                            backgroundColor: 'var(--color-status-error)',
                            color: 'var(--color-white)',
                            fontWeight: 'var(--font-weight-bold)'
                          }}
                        />
                      )}
                    </Box>

                    {/* Product Content */}
                    <CardContent sx={{
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column',
                      p: 2
                    }}>
                      {/* Brand */}
                      <Typography variant="caption" sx={{
                        color: 'var(--color-text-secondary)',
                        mb: 0.5,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        {product.brand}
                      </Typography>

                      {/* Product Name */}
                      <Typography variant="h6" sx={{
                        color: 'var(--color-text-heading)',
                        fontWeight: 'var(--font-weight-medium)',
                        mb: 1,
                        lineHeight: 1.3
                      }}>
                        {product.name}
                      </Typography>

                      {/* Description */}
                      <Typography variant="body2" sx={{
                        color: 'var(--color-text-secondary)',
                        mb: 2,
                        flex: 1
                      }}>
                        {product.description}
                      </Typography>

                      {/* Rating */}
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Rating
                          value={product.rating}
                          precision={0.1}
                          readOnly
                          size="small"
                        />
                        <Typography variant="body2" sx={{
                          ml: 1,
                          color: 'var(--color-text-secondary)'
                        }}>
                          ({product.reviews})
                        </Typography>
                      </Box>

                      {/* Variants */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" sx={{
                          color: 'var(--color-text-secondary)',
                          mb: 1,
                          display: 'block'
                        }}>
                          Variants:
                        </Typography>
                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                          {product.variants.slice(0, 2).map((variant, index) => (
                            <Chip
                              key={index}
                              label={variant}
                              size="small"
                              variant="outlined"
                              sx={{
                                fontSize: '0.7rem',
                                height: 20,
                                borderColor: 'var(--theme-border-color)',
                                color: 'var(--color-text-secondary)'
                              }}
                            />
                          ))}
                          {product.variants.length > 2 && (
                            <Chip
                              label={`+${product.variants.length - 2}`}
                              size="small"
                              variant="outlined"
                              sx={{
                                fontSize: '0.7rem',
                                height: 20,
                                borderColor: 'var(--theme-border-color)',
                                color: 'var(--color-text-secondary)'
                              }}
                            />
                          )}
                        </Stack>
                      </Box>

                      {/* Price Section */}
                      <Box sx={{ mb: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Typography variant="h6" sx={{
                            color: 'var(--color-primary-brand)',
                            fontWeight: 'var(--font-weight-bold)'
                          }}>
                            ${product.price}
                          </Typography>
                          {product.originalPrice > product.price && (
                            <Typography variant="body2" sx={{
                              color: 'var(--color-text-muted)',
                              textDecoration: 'line-through'
                            }}>
                              ${product.originalPrice}
                            </Typography>
                          )}
                        </Stack>
                      </Box>

                      {/* Action Buttons */}
                      <Stack direction="row" spacing={1}>
                        <Button
                          variant="contained"
                          startIcon={<CartIcon />}
                          onClick={() => addToCart(product.id)}
                          disabled={!product.inStock}
                          fullWidth
                          sx={{
                            backgroundColor: 'var(--button-primary-bg)',
                            color: 'var(--button-color)',
                            '&:hover': {
                              backgroundColor: 'var(--button-primary-hover-bg)'
                            },
                            '&:disabled': {
                              backgroundColor: 'var(--color-text-muted)',
                              color: 'var(--color-white)'
                            }
                          }}
                        >
                          {cart.has(product.id) ? 'Added' : 'Add to Cart'}
                        </Button>

                        <Button
                          variant="outlined"
                          size="small"
                          sx={{
                            borderColor: 'var(--color-primary-brand)',
                            color: 'var(--color-primary-brand)',
                            '&:hover': {
                              backgroundColor: 'var(--color-primary-brand)',
                              color: 'var(--color-white)'
                            }
                          }}
                        >
                          Details
                        </Button>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* No Results Message */}
            {filteredProducts.length === 0 && (
              <Paper sx={{
                p: 4,
                textAlign: 'center',
                backgroundColor: 'var(--card-bg)',
                boxShadow: 'var(--card-shadow)'
              }}>
                <Typography variant="h6" sx={{
                  color: 'var(--color-text-heading)',
                  mb: 2
                }}>
                  No products found
                </Typography>
                <Typography variant="body1" sx={{
                  color: 'var(--color-text-secondary)',
                  mb: 3
                }}>
                  Try adjusting your search criteria or filters
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('All');
                    setSelectedBrand('All');
                    setPriceRange([0, 200]);
                  }}
                  sx={{
                    backgroundColor: 'var(--button-primary-bg)',
                    '&:hover': {
                      backgroundColor: 'var(--button-primary-hover-bg)'
                    }
                  }}
                >
                  Clear All Filters
                </Button>
              </Paper>
            )}
          </Grid>
        </Grid>

        {/* Floating Action Button for Cart */}
        <Box sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1000
        }}>
          <Badge badgeContent={cart.size} color="error">
            <IconButton
              sx={{
                backgroundColor: 'var(--color-primary-brand)',
                color: 'var(--color-white)',
                width: 56,
                height: 56,
                '&:hover': {
                  backgroundColor: 'var(--button-primary-hover-bg)',
                  transform: 'scale(1.1)'
                },
                transition: 'var(--transition-ease-in-out)'
              }}
            >
              <CartIcon />
            </IconButton>
          </Badge>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;