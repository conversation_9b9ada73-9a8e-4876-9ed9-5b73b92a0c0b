import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Ty<PERSON>graphy,
  Button,
  Card,
  CardContent,
  CardMedia,
  Rating,
  Chip,
  Tabs,
  Tab,
  Breadcrumbs,
  Link,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Badge,
  Tooltip,
  Alert,
  LinearProgress,
  Drawer,
  Switch,
  FormControlLabel,
  TextField,
  Slider
} from '@mui/material';
import {
  ShoppingCart,
  Favorite,
  Share,
  LocalShipping,
  Security,
  CheckCircle,
  ArrowBack,
  ArrowForward,
  ZoomIn,
  ExpandMore,
  Settings,
  Visibility,
  VisibilityOff,
  Edit,
  Save,
  Cancel,
  Info,
  Warning,
  Build,
  Speed,
  Star,
  Inventory,
  AttachMoney,
  Category
} from '@mui/icons-material';
import { sampleProducts, adminConfigOptions } from '../data/productData';

const Product = ({ productId = 'AM001', isAdmin = false }) => {
  // State management
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedVehicle, setSelectedVehicle] = useState('');
  const [adminDrawerOpen, setAdminDrawerOpen] = useState(false);
  const [adminConfig, setAdminConfig] = useState({});
  const [editMode, setEditMode] = useState(false);

  // Load product data
  useEffect(() => {
    const product = sampleProducts.find(p => p.id === productId);
    if (product) {
      setSelectedProduct(product);
      setAdminConfig(product.adminConfig);
    }
  }, [productId]);

  // Event handlers
  const handleImageChange = (direction) => {
    if (!selectedProduct) return;
    const totalImages = selectedProduct.media.images.length;

    if (direction === 'next') {
      setSelectedImageIndex((prev) => (prev + 1) % totalImages);
    } else {
      setSelectedImageIndex((prev) => (prev - 1 + totalImages) % totalImages);
    }
  };

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  const handleAddToCart = () => {
    console.log('Adding to cart:', { productId, quantity, selectedVehicle });
    // Implement cart functionality
  };

  const handleAdminConfigChange = (field, value) => {
    setAdminConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveAdminConfig = () => {
    console.log('Saving admin config:', adminConfig);
    setEditMode(false);
    // Implement save functionality
  };

  if (!selectedProduct) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
          Loading product...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 2 }}>
      {/* Admin Controls */}
      {isAdmin && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          zIndex: 1000,
          display: 'flex',
          gap: 1
        }}>
          <Tooltip title="Admin Configuration">
            <IconButton
              onClick={() => setAdminDrawerOpen(true)}
              sx={{
                bgcolor: 'var(--color-primary-brand)',
                color: 'white',
                '&:hover': {
                  bgcolor: 'var(--button-primary-hover-bg-brand)'
                }
              }}
            >
              <Settings />
            </IconButton>
          </Tooltip>

          <Tooltip title={adminConfig.isVisible ? 'Visible to Customers' : 'Hidden from Customers'}>
            <IconButton
              sx={{
                bgcolor: adminConfig.isVisible ? 'var(--color-status-success)' : 'var(--color-status-error)',
                color: 'white'
              }}
            >
              {adminConfig.isVisible ? <Visibility /> : <VisibilityOff />}
            </IconButton>
          </Tooltip>
        </Box>
      )}

      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link color="inherit" href="/">
          Home
        </Link>
        <Link color="inherit" href={`/category/${selectedProduct.category}`}>
          {selectedProduct.category.charAt(0).toUpperCase() + selectedProduct.category.slice(1)}
        </Link>
        <Link color="inherit" href={`/type/${selectedProduct.type}`}>
          {selectedProduct.type.replace('_', ' ').charAt(0).toUpperCase() + selectedProduct.type.replace('_', ' ').slice(1)}
        </Link>
        <Typography color="text.primary">{selectedProduct.name}</Typography>
      </Breadcrumbs>

      <Grid container spacing={4}>
        {/* Left Column - Product Images */}
        <Grid item xs={12} md={6}>
          <Box sx={{ position: 'sticky', top: 20 }}>
            {/* Main Product Image */}
            <Card sx={{ mb: 2, position: 'relative' }}>
              <CardMedia
                component="img"
                height="500"
                image={selectedProduct.media.images[selectedImageIndex]}
                alt={selectedProduct.name}
                sx={{ objectFit: 'contain', bgcolor: 'var(--theme-background-secondary)' }}
              />

              {/* Image Navigation */}
              {selectedProduct.media.images.length > 1 && (
                <>
                  <IconButton
                    sx={{
                      position: 'absolute',
                      left: 16,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': { bgcolor: 'white' }
                    }}
                    onClick={() => handleImageChange('prev')}
                  >
                    <ArrowBack />
                  </IconButton>

                  <IconButton
                    sx={{
                      position: 'absolute',
                      right: 16,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': { bgcolor: 'white' }
                    }}
                    onClick={() => handleImageChange('next')}
                  >
                    <ArrowForward />
                  </IconButton>
                </>
              )}

              {/* Zoom Button */}
              <IconButton
                sx={{
                  position: 'absolute',
                  top: 16,
                  right: 16,
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                  '&:hover': { bgcolor: 'white' }
                }}
              >
                <ZoomIn />
              </IconButton>

              {/* Stock Status Badge */}
              <Chip
                label={selectedProduct.inventory.inStock ? 'In Stock' : 'Out of Stock'}
                color={selectedProduct.inventory.inStock ? 'success' : 'error'}
                sx={{
                  position: 'absolute',
                  top: 16,
                  left: 16
                }}
              />
            </Card>

            {/* Thumbnail Images */}
            {selectedProduct.media.images.length > 1 && (
              <Grid container spacing={1}>
                {selectedProduct.media.images.map((image, index) => (
                  <Grid item xs={3} key={index}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        border: selectedImageIndex === index
                          ? '2px solid var(--color-primary-brand)'
                          : '2px solid var(--theme-border-color)',
                        transition: 'var(--transition-fast)'
                      }}
                      onClick={() => setSelectedImageIndex(index)}
                    >
                      <CardMedia
                        component="img"
                        height="80"
                        image={image}
                        alt={`${selectedProduct.name} ${index + 1}`}
                        sx={{ objectFit: 'contain' }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Grid>

        {/* Right Column - Product Details */}
        <Grid item xs={12} md={6}>
          <Box>
            {/* Product Header */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h4" component="h1" gutterBottom>
                {selectedProduct.name}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Typography variant="h6" color="text.secondary">
                  {selectedProduct.brand}
                </Typography>
                <Chip
                  label={`SKU: ${selectedProduct.sku}`}
                  size="small"
                  variant="outlined"
                />
              </Box>

              {/* Rating and Reviews */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Rating
                  value={selectedProduct.reviews.averageRating}
                  precision={0.1}
                  readOnly
                  size="small"
                />
                <Typography variant="body2" color="text.secondary">
                  {selectedProduct.reviews.averageRating} ({selectedProduct.reviews.totalReviews} reviews)
                </Typography>
              </Box>
            </Box>

            {/* Pricing */}
            <Card sx={{ mb: 3, p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                {selectedProduct.pricing.discountPercentage > 0 && (
                  <Typography
                    variant="h6"
                    sx={{
                      textDecoration: 'line-through',
                      color: 'text.secondary'
                    }}
                  >
                    ${selectedProduct.pricing.msrp.toFixed(2)}
                  </Typography>
                )}
                <Typography
                  variant="h4"
                  sx={{
                    color: 'var(--color-primary-brand)',
                    fontWeight: 'bold'
                  }}
                >
                  ${selectedProduct.pricing.retailPrice.toFixed(2)}
                </Typography>
                {selectedProduct.pricing.discountPercentage > 0 && (
                  <Chip
                    label={`${selectedProduct.pricing.discountPercentage}% OFF`}
                    color="error"
                    size="small"
                  />
                )}
              </Box>

              {/* Stock Information */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Inventory fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  {selectedProduct.inventory.quantity} units in stock
                </Typography>
                {selectedProduct.inventory.quantity <= selectedProduct.inventory.lowStockThreshold && (
                  <Chip label="Low Stock" color="warning" size="small" />
                )}
              </Box>
            </Card>

            {/* Vehicle Compatibility */}
            {selectedProduct.compatibility.vehicles && selectedProduct.compatibility.vehicles.length > 0 && (
              <Card sx={{ mb: 3, p: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Build color="primary" />
                  Vehicle Compatibility
                </Typography>

                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Select Your Vehicle</InputLabel>
                  <Select
                    value={selectedVehicle}
                    label="Select Your Vehicle"
                    onChange={(e) => setSelectedVehicle(e.target.value)}
                  >
                    {selectedProduct.compatibility.vehicles.map((vehicle, index) => (
                      <MenuItem key={index} value={`${vehicle.make}-${vehicle.model}`}>
                        {vehicle.make} {vehicle.model} ({vehicle.years[0]}-{vehicle.years[vehicle.years.length - 1]})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {selectedVehicle && (
                  <Alert severity="success" sx={{ mt: 1 }}>
                    <Typography variant="body2">
                      ✓ Compatible with your selected vehicle
                    </Typography>
                  </Alert>
                )}
              </Card>
            )}

            {/* Quantity and Add to Cart */}
            <Card sx={{ mb: 3, p: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <FormControl sx={{ minWidth: 120 }}>
                  <InputLabel>Quantity</InputLabel>
                  <Select
                    value={quantity}
                    label="Quantity"
                    onChange={(e) => setQuantity(e.target.value)}
                  >
                    {[...Array(Math.min(10, selectedProduct.inventory.quantity))].map((_, i) => (
                      <MenuItem key={i + 1} value={i + 1}>
                        {i + 1}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={<ShoppingCart />}
                  onClick={handleAddToCart}
                  disabled={!selectedProduct.inventory.inStock}
                  sx={{
                    flex: 1,
                    bgcolor: 'var(--color-primary-brand)',
                    '&:hover': {
                      bgcolor: 'var(--button-primary-hover-bg-brand)'
                    }
                  }}
                >
                  Add to Cart
                </Button>
              </Box>

              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<Favorite />}
                  sx={{ flex: 1 }}
                >
                  Wishlist
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Share />}
                  sx={{ flex: 1 }}
                >
                  Share
                </Button>
              </Box>
            </Card>

            {/* Key Features */}
            <Card sx={{ mb: 3, p: 2 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Speed color="primary" />
                Key Features
              </Typography>
              <List dense>
                {selectedProduct.features.map((feature, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <CheckCircle color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={feature} />
                  </ListItem>
                ))}
              </List>
            </Card>

            {/* Shipping Information */}
            <Card sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocalShipping color="primary" />
                Shipping & Returns
              </Typography>
              <List dense>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={selectedProduct.shipping.freeShippingEligible ? "Free Shipping" : "Standard Shipping Rates Apply"}
                    secondary={`Weight: ${selectedProduct.shipping.weight} lbs`}
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <Security color="primary" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="30-Day Return Policy"
                    secondary="Easy returns with prepaid shipping label"
                  />
                </ListItem>
              </List>
            </Card>
          </Box>
        </Grid>
      </Grid>

      {/* Product Information Tabs */}
      <Box sx={{ mt: 4 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Description" />
          <Tab label="Specifications" />
          <Tab label="Compatibility" />
          <Tab label="Reviews" />
          <Tab label="Installation" />
        </Tabs>

        <Box sx={{ mt: 3 }}>
          {/* Description Tab */}
          {activeTab === 0 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {selectedProduct.description}
              </Typography>

              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Product Benefits
              </Typography>
              <Grid container spacing={2}>
                {selectedProduct.features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      <CheckCircle color="success" fontSize="small" sx={{ mt: 0.5 }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Card>
          )}

          {/* Specifications Tab */}
          {activeTab === 1 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Technical Specifications
              </Typography>
              <TableContainer>
                <Table>
                  <TableBody>
                    {Object.entries(selectedProduct.specifications).map(([key, value]) => (
                      <TableRow key={key}>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'medium', width: '40%' }}>
                          {key}
                        </TableCell>
                        <TableCell>{value}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
          )}

          {/* Compatibility Tab */}
          {activeTab === 2 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Vehicle Compatibility
              </Typography>

              {selectedProduct.compatibility.vehicles.map((vehicle, index) => (
                <Accordion key={index}>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography variant="subtitle1">
                      {vehicle.make} {vehicle.model}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Model Years:
                        </Typography>
                        <Typography variant="body2">
                          {vehicle.years.join(', ')}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Compatible Engines:
                        </Typography>
                        {vehicle.engines.map((engine, engineIndex) => (
                          <Typography key={engineIndex} variant="body2">
                            • {engine}
                          </Typography>
                        ))}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}

              {selectedProduct.compatibility.oem_numbers && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    OEM Part Numbers:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedProduct.compatibility.oem_numbers.map((number, index) => (
                      <Chip key={index} label={number} variant="outlined" size="small" />
                    ))}
                  </Box>
                </Box>
              )}
            </Card>
          )}

          {/* Reviews Tab */}
          {activeTab === 3 && (
            <Card sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Rating
                  value={selectedProduct.reviews.averageRating}
                  precision={0.1}
                  readOnly
                />
                <Typography variant="h6">
                  {selectedProduct.reviews.averageRating} out of 5
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ({selectedProduct.reviews.totalReviews} reviews)
                </Typography>
              </Box>

              {/* Rating Distribution */}
              <Box sx={{ mb: 3 }}>
                {[5, 4, 3, 2, 1].map((rating) => (
                  <Box key={rating} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 60 }}>
                      {rating} stars
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(selectedProduct.reviews.ratingDistribution[rating] / selectedProduct.reviews.totalReviews) * 100}
                      sx={{ flex: 1, height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body2" sx={{ minWidth: 40 }}>
                      {selectedProduct.reviews.ratingDistribution[rating]}
                    </Typography>
                  </Box>
                ))}
              </Box>

              <Button variant="outlined" startIcon={<Edit />}>
                Write a Review
              </Button>
            </Card>
          )}

          {/* Installation Tab */}
          {activeTab === 4 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Installation Information
              </Typography>

              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Professional installation recommended. Installation time: approximately 30-45 minutes.
                </Typography>
              </Alert>

              {selectedProduct.media.documents && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Available Documents:
                  </Typography>
                  <List>
                    {selectedProduct.media.documents.map((doc, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Info color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={doc.title}
                          secondary={`${doc.type} Document`}
                        />
                        <Button variant="outlined" size="small">
                          Download
                        </Button>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Card>
          )}
        </Box>
      </Box>

      {/* Admin Configuration Drawer */}
      {isAdmin && (
        <Drawer
          anchor="right"
          open={adminDrawerOpen}
          onClose={() => setAdminDrawerOpen(false)}
          sx={{
            '& .MuiDrawer-paper': {
              width: 400,
              p: 3
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6">
              Admin Configuration
            </Typography>
            <Box>
              {editMode ? (
                <>
                  <IconButton onClick={saveAdminConfig} color="primary">
                    <Save />
                  </IconButton>
                  <IconButton onClick={() => setEditMode(false)}>
                    <Cancel />
                  </IconButton>
                </>
              ) : (
                <IconButton onClick={() => setEditMode(true)}>
                  <Edit />
                </IconButton>
              )}
            </Box>
          </Box>

          {/* Visibility Settings */}
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Visibility />
                Visibility Settings
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {adminConfigOptions.visibilityFields.map((field) => (
                <FormControlLabel
                  key={field.key}
                  control={
                    <Switch
                      checked={adminConfig[field.key] || false}
                      onChange={(e) => handleAdminConfigChange(field.key, e.target.checked)}
                      disabled={!editMode}
                    />
                  }
                  label={field.label}
                  sx={{ display: 'block', mb: 1 }}
                />
              ))}
            </AccordionDetails>
          </Accordion>

          {/* Pricing Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AttachMoney />
                Pricing Visibility
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showMSRP || false}
                    onChange={(e) => handleAdminConfigChange('showMSRP', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show MSRP"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showDealerPrice || false}
                    onChange={(e) => handleAdminConfigChange('showDealerPrice', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Dealer Price"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showDiscount || true}
                    onChange={(e) => handleAdminConfigChange('showDiscount', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Discount Badge"
                sx={{ display: 'block', mb: 1 }}
              />
            </AccordionDetails>
          </Accordion>

          {/* Inventory Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Inventory />
                Inventory Display
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showStockCount || true}
                    onChange={(e) => handleAdminConfigChange('showStockCount', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Stock Count"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showLowStockWarning || true}
                    onChange={(e) => handleAdminConfigChange('showLowStockWarning', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Low Stock Warning"
                sx={{ display: 'block', mb: 1 }}
              />
            </AccordionDetails>
          </Accordion>

          {/* Feature Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Category />
                Feature Display
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showCompatibility || true}
                    onChange={(e) => handleAdminConfigChange('showCompatibility', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Compatibility Section"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showReviews || true}
                    onChange={(e) => handleAdminConfigChange('showReviews', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Reviews"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showInstallation || true}
                    onChange={(e) => handleAdminConfigChange('showInstallation', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Installation Info"
                sx={{ display: 'block', mb: 1 }}
              />
            </AccordionDetails>
          </Accordion>

          {/* Display Order */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Display Order
            </Typography>
            <Slider
              value={adminConfig.displayOrder || 1}
              onChange={(_, value) => handleAdminConfigChange('displayOrder', value)}
              min={1}
              max={100}
              marks
              valueLabelDisplay="auto"
              disabled={!editMode}
            />
          </Box>

          {/* Product Status */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'var(--theme-background-primary)', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Product Status
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Created: {new Date(adminConfig.createdDate).toLocaleDateString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last Modified: {new Date(adminConfig.lastModified).toLocaleDateString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Modified By: {adminConfig.lastModifiedBy}
            </Typography>
          </Box>
        </Drawer>
      )}
    </Container>
  );
};

export default Product;