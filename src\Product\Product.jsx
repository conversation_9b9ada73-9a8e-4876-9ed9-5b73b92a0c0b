import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  IconButton,
  Chip,
  <PERSON>ing,
  FormControl,
  InputLabel,
  Select,
  MenuItem,

  Switch,
  FormControlLabel,
  Card,
  CardContent,
  CardMedia,
  Stack,
  Paper,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,

  Refresh as RefreshIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import './Product.css';
import productData from './productData.json';

const Product = () => {
  // Search and Display States
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('relevance');
  const [viewMode, setViewMode] = useState('grid');

  // User Interaction States
  const [favorites, setFavorites] = useState(new Set());
  const [cart, setCart] = useState(new Set());

  // Configurator States - Control what's visible in product list
  const [showConfig, setShowConfig] = useState({
    showBrand: true,
    showDescription: true,
    showRating: true,
    showVariants: true,
    showOriginalPrice: true,
    showStockBadge: true,
    showSaleBadge: true,
    showFavoriteButton: true,
    showAddToCart: true,
    showDetailsButton: true,
    compactView: false,
    showProductImages: true,
    showReviewCount: true
  });

  // Filter Functions
  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const addToCart = (productId) => {
    const newCart = new Set(cart);
    newCart.add(productId);
    setCart(newCart);
  };

  const filteredProducts = productData.products.filter(product => {
    const matchesSearch = searchQuery === '' ||
                         product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  const handleConfigChange = (configKey) => (event) => {
    setShowConfig(prev => ({
      ...prev,
      [configKey]: event.target.checked
    }));
  };

  const resetConfiguration = () => {
    setShowConfig({
      showBrand: true,
      showDescription: true,
      showRating: true,
      showVariants: true,
      showOriginalPrice: true,
      showStockBadge: true,
      showSaleBadge: true,
      showFavoriteButton: true,
      showAddToCart: true,
      showDetailsButton: true,
      compactView: false,
      showProductImages: true,
      showReviewCount: true
    });
  };

  return (
    <Box className="product-page-container">
      <Container maxWidth="xl">
        {/* Header */}
       

        {/* 3-Panel Layout - Wireframe Style */}
        <Box className="three-panel-layout">
          {/* Left Panel - FILTER */}
          <Paper className="filters-panel">
            <Box className="panel-header">
              <Box>
                <Typography className="panel-title">FILTER</Typography>
                <Typography className="panel-subtitle">Filters</Typography>
              </Box>
            </Box>

            {/* Filter content area - empty for now */}
            <Box className="filter-content">
              <Typography variant="body2" className="filter-placeholder">
                Filter options will be added here
              </Typography>
            </Box>
          </Paper>

          {/* Center Panel - Product */}
          <Paper className="products-main-panel">
            

            {/* Main Products Grid */}
            {filteredProducts.length > 0 ? (
              <Box className={viewMode === 'grid' ? 'products-grid' : 'products-list'}>
                {filteredProducts.map((product) => (
                  <Card
                    key={product.id}
                    className={`product-card ${showConfig.compactView ? 'product-card-compact' : ''} ${product.isFeatured ? 'product-card-featured' : ''} ${product.isOnSale ? 'product-card-sale' : ''}`}
                  >
                    {showConfig.showProductImages && (
                      <Box className="product-image-container">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="product-image"
                          style={{ height: showConfig.compactView ? '150px' : '200px' }}
                        />

                        <Box className="product-badges">
                          {showConfig.showStockBadge && (
                            <span className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}>
                              {product.inStock ? 'In Stock' : 'Out of Stock'}
                            </span>
                          )}
                          {showConfig.showSaleBadge && product.originalPrice > product.price && (
                            <span className="stock-badge sale-badge">
                              -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                            </span>
                          )}
                        </Box>

                        {showConfig.showFavoriteButton && (
                          <button
                            className="favorite-button"
                            onClick={() => toggleFavorite(product.id)}
                          >
                            {favorites.has(product.id) ? (
                              <FavoriteIcon className="favorite-active" />
                            ) : (
                              <FavoriteBorderIcon className="favorite-inactive" />
                            )}
                          </button>
                        )}
                      </Box>
                    )}

                    <CardContent className="product-content">
                      {showConfig.showBrand && (
                        <Typography className="product-brand">
                          {product.brand}
                        </Typography>
                      )}

                      <Typography variant="h6" className="product-name">
                        {product.name}
                      </Typography>

                      {showConfig.showDescription && !showConfig.compactView && (
                        <Typography className="product-description">
                          {product.description}
                        </Typography>
                      )}

                      {showConfig.showRating && (
                        <Box className="product-rating">
                          <Rating value={product.rating} precision={0.1} readOnly size="small" />
                          {showConfig.showReviewCount && (
                            <Typography className="rating-text">
                              ({product.reviews})
                            </Typography>
                          )}
                        </Box>
                      )}

                      {showConfig.showVariants && !showConfig.compactView && (
                        <Box className="product-variants">
                          <Typography className="variants-label">Variants:</Typography>
                          <Box className="variants-chips">
                            {product.variants.slice(0, 3).map((variant, index) => (
                              <Chip
                                key={index}
                                label={variant}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            ))}
                            {product.variants.length > 3 && (
                              <Chip
                                label={`+${product.variants.length - 3}`}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            )}
                          </Box>
                        </Box>
                      )}

                      <Box className="product-pricing">
                        <Typography className="price-current">
                          ${product.price}
                        </Typography>
                        {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                          <Typography className="price-original">
                            ${product.originalPrice}
                          </Typography>
                        )}
                      </Box>

                      {!showConfig.compactView && (
                        <Box className="product-actions">
                          {showConfig.showAddToCart && (
                            <Button
                              variant="contained"
                              startIcon={<CartIcon />}
                              onClick={() => addToCart(product.id)}
                              disabled={!product.inStock}
                              className="add-cart-button"
                              fullWidth={!showConfig.showDetailsButton}
                            >
                              {cart.has(product.id) ? 'Added' : 'Add to Cart'}
                            </Button>
                          )}

                          {showConfig.showDetailsButton && (
                            <Button
                              variant="outlined"
                              size="small"
                              className="details-button"
                            >
                              Details
                            </Button>
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Box className="no-results">
                <Typography variant="h6" className="no-results-title">
                  No products found
                </Typography>
                <Typography className="no-results-text">
                  Try adjusting your search criteria or filters
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => {
                    setSearchQuery('');
                  }}
                  className="clear-filters-button"
                >
                  Clear Search
                </Button>
              </Box>
            )}
          </Paper>

          {/* Right Panel - Configurator */}
          <Paper className="configurator-panel">
            <Box className="panel-header">
              <Box>
                <Typography className="panel-title">Configurator</Typography>
                <Typography className="panel-subtitle">Product Management Settings</Typography>
              </Box>
            </Box>

            <Box className="config-content">
              {/* 1. Basic Product Information */}
              <Box className="config-group">
                <Typography className="config-group-title">1. Basic Product Information</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Product SKU</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Status</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Short Description</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Long Description</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Images</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Videos</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Internal Product ID</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 2. Pricing & Financials */}
              <Box className="config-group">
                <Typography className="config-group-title">2. Pricing & Financials</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Sale Price</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Sale Start/End Date</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Tax Class/Status</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Cost of Goods (COG)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Profit Margin</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Commission Rate / Fee</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 3. Inventory & Shipping */}
              <Box className="config-group">
                <Typography className="config-group-title">3. Inventory & Shipping</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Stock Quantity</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Allow Backorders?</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Low Stock Threshold</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Weight (for shipping)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Dimensions (L/W/H)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Shipping Class</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Warehouse/Location</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Supplier Name</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 4. Organization & Attributes */}
              <Box className="config-group">
                <Typography className="config-group-title">4. Organization & Attributes</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Category</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Product Tags / Keywords</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Attributes (Color, Size)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Variations (and SKUs)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Brand</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 5. Marketing & SEO */}
              <Box className="config-group">
                <Typography className="config-group-title">5. Marketing & SEO</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">SEO Title</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">SEO Meta Description</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Upsells</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Cross-sells</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Featured Product?</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 6. Analytics & Performance */}
              <Box className="config-group">
                <Typography className="config-group-title">6. Analytics & Performance</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Page Views / Visits</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Sales Count (Units Sold)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Total Revenue</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Customer Reviews & Ratings</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Review Approval Status</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Conversion Rate</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Platform-wide Analytics</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 7. Internal & System Data */}
              <Box className="config-group">
                <Typography className="config-group-title">7. Internal & System Data</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Internal Notes</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">User/Owner ID</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Date Created</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Last Modified By (Admin)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Integration-specific IDs</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 8. Geo Applicability */}
              <Box className="config-group">
                <Typography className="config-group-title">8. Geo Applicability</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of Geo (pincode)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All (pincode)</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Multiple selection criteria</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 9. Quantity Based */}
              <Box className="config-group">
                <Typography className="config-group-title">9. Quantity Based</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">SKU & Quantity</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Price Application</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 10. SKU Selection */}
              <Box className="config-group">
                <Typography className="config-group-title">10. SKU Selection</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of Products applicable</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 11. Customers */}
              <Box className="config-group">
                <Typography className="config-group-title">11. Customers</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of customers</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Multiple Selection</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 12. Assets (Automotive) */}
              <Box className="config-group">
                <Typography className="config-group-title">12. Assets (Automotive)</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Brand, Product type, model</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Year, serial#'s</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Multiple Selection</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">All</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Filtering</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 13. Discount Applicable */}
              <Box className="config-group">
                <Typography className="config-group-title">13. Discount Applicable</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Quantity & Discount</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Quantity and Rate</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 14. Delivery Conditions */}
              <Box className="config-group">
                <Typography className="config-group-title">14. Delivery Conditions</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">Delivery Terms</Typography>
                    <Switch size="small" />
                  </Box>
                  <Box className="config-item">
                    <Typography className="config-item-label">Shipping Conditions</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 15. Combination Offers */}
              <Box className="config-group">
                <Typography className="config-group-title">15. Combination Offers</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">List of SKU (Quantity, Rate)</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>

              {/* 16. Order Date Validity */}
              <Box className="config-group">
                <Typography className="config-group-title">16. Order Date Validity</Typography>
                <Box className="config-items">
                  <Box className="config-item">
                    <Typography className="config-item-label">From & To</Typography>
                    <Switch size="small" />
                  </Box>
                </Box>
              </Box>
            </Box>

            {/* Configuration Actions */}
            <Box className="config-actions">
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                className="config-button"
                fullWidth
              >
                Save Configuration
              </Button>

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetConfiguration}
                className="config-button-secondary"
                fullWidth
              >
                Reset to Default
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;