import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  TextField,
  Button,
  IconButton,
  Chip,
  Rating,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Badge,
  Stack,
  Paper
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon
} from '@mui/icons-material';
import './Product.css';
import productData from './productData.json';

const Product = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedBrand, setSelectedBrand] = useState('All');
  const [priceRange, setPriceRange] = useState([productData.priceRange.min, productData.priceRange.max]);
  const [sortBy, setSortBy] = useState('relevance');
  const [viewMode, setViewMode] = useState('grid');
  const [favorites, setFavorites] = useState(new Set());
  const [cart, setCart] = useState(new Set());

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const addToCart = (productId) => {
    const newCart = new Set(cart);
    newCart.add(productId);
    setCart(newCart);
  };

  const filteredProducts = productData.products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    const matchesBrand = selectedBrand === 'All' || product.brand === selectedBrand;
    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesBrand && matchesPrice;
  });

  return (
    <Box className="product-container">
      <Container maxWidth="xl">
        {/* Header Section */}
        <Box className="product-header">
          <Typography variant="h3" className="product-title">
            Product Search
          </Typography>

          {/* Search Bar */}
          <Paper className="search-paper">
            <Stack className="search-container">
              <TextField
                fullWidth
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: <SearchIcon className="search-icon" />
                  }
                }}
              />
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                className="search-button"
              >
                Search
              </Button>
            </Stack>
          </Paper>
        </Box>

        {/* Main Content - Side by Side Layout */}
        <Grid container spacing={3}>
          {/* Filters Sidebar */}
          <Grid item xs={12} md={3}>
            <Paper className="filters-sidebar">
              <Typography variant="h6" className="filters-title">
                Filters
              </Typography>

              {/* Category Filter */}
              <Box className="filter-section">
                <Typography variant="subtitle1" className="filter-label">
                  Category
                </Typography>
                <FormControl fullWidth>
                  <Select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    {productData.categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              {/* Brand Filter */}
              <Box className="filter-section">
                <Typography variant="subtitle1" className="filter-label">
                  Brand
                </Typography>
                <FormControl fullWidth>
                  <Select
                    value={selectedBrand}
                    onChange={(e) => setSelectedBrand(e.target.value)}
                  >
                    {productData.brands.map((brand) => (
                      <MenuItem key={brand} value={brand}>
                        {brand}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              {/* Price Range Filter */}
              <Box className="filter-section">
                <Typography variant="subtitle1" className="filter-label">
                  Price Range
                </Typography>
                <Slider
                  value={priceRange}
                  onChange={(_, newValue) => setPriceRange(newValue)}
                  valueLabelDisplay="auto"
                  min={productData.priceRange.min}
                  max={productData.priceRange.max}
                  marks={productData.priceRange.marks}
                />
                <Typography variant="body2" className="price-range-text">
                  ${priceRange[0]} - ${priceRange[1]}
                </Typography>
              </Box>

              {/* Quick Filters */}
              <Box className="filter-section">
                <Typography variant="subtitle1" className="filter-label">
                  Quick Filters
                </Typography>
                <Stack className="quick-filters">
                  <Chip
                    label="In Stock"
                    variant="outlined"
                    clickable
                    className="quick-filter-chip"
                  />
                  <Chip
                    label="On Sale"
                    variant="outlined"
                    clickable
                    className="quick-filter-sale"
                  />
                  <Chip
                    label="Top Rated"
                    variant="outlined"
                    clickable
                    className="quick-filter-rated"
                  />
                </Stack>
              </Box>
            </Paper>
          </Grid>

          {/* Products Section - Right Side */}
          <Grid item xs={12} md={9}>
            {/* Toolbar */}
            <Paper className="toolbar-paper">
              <Stack className="toolbar-container">
                <Typography variant="body1">
                  {filteredProducts.length} products found
                </Typography>

                <Stack className="toolbar-controls">
                  {/* Sort Dropdown */}
                  <FormControl size="small">
                    <InputLabel>Sort by</InputLabel>
                    <Select
                      value={sortBy}
                      label="Sort by"
                      onChange={(e) => setSortBy(e.target.value)}
                    >
                      {productData.sortOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  {/* View Mode Toggle */}
                  <Stack className="view-toggle">
                    <IconButton
                      onClick={() => setViewMode('grid')}
                      className={viewMode === 'grid' ? 'view-button-active' : 'view-button-inactive'}
                    >
                      <ViewModuleIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => setViewMode('list')}
                      className={viewMode === 'list' ? 'view-button-active' : 'view-button-inactive'}
                    >
                      <ViewListIcon />
                    </IconButton>
                  </Stack>
                </Stack>
              </Stack>
            </Paper>

            {/* Products Grid */}
            <Grid container spacing={3}>
              {filteredProducts.map((product) => (
                <Grid
                  item
                  xs={12}
                  sm={viewMode === 'grid' ? 6 : 12}
                  md={viewMode === 'grid' ? 4 : 12}
                  lg={viewMode === 'grid' ? 3 : 12}
                  key={product.id}
                >
                  <Card className="product-card">
                    {/* Product Image */}
                    <Box className="product-image-container">
                      <CardMedia
                        component="img"
                        height={200}
                        image={product.image}
                        alt={product.name}
                        className="product-image"
                      />

                      {/* Stock Badge */}
                      <Chip
                        label={product.inStock ? 'In Stock' : 'Out of Stock'}
                        size="small"
                        className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}
                      />

                      {/* Favorite Button */}
                      <IconButton
                        onClick={() => toggleFavorite(product.id)}
                        className="favorite-button"
                      >
                        {favorites.has(product.id) ? (
                          <FavoriteIcon className="favorite-active" />
                        ) : (
                          <FavoriteBorderIcon className="favorite-inactive" />
                        )}
                      </IconButton>

                      {/* Sale Badge */}
                      {product.originalPrice > product.price && (
                        <Chip
                          label={`-${Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%`}
                          size="small"
                          className="sale-badge"
                        />
                      )}
                    </Box>

                    {/* Product Content */}
                    <CardContent className="product-content">
                      {/* Brand */}
                      <Typography variant="caption" className="product-brand">
                        {product.brand}
                      </Typography>

                      {/* Product Name */}
                      <Typography variant="h6" className="product-name">
                        {product.name}
                      </Typography>

                      {/* Description */}
                      <Typography variant="body2" className="product-description">
                        {product.description}
                      </Typography>

                      {/* Rating */}
                      <Box className="rating-container">
                        <Rating
                          value={product.rating}
                          precision={0.1}
                          readOnly
                          size="small"
                        />
                        <Typography variant="body2" className="rating-text">
                          ({product.reviews})
                        </Typography>
                      </Box>

                      {/* Variants */}
                      <Box className="variants-section">
                        <Typography variant="caption" className="variants-label">
                          Variants:
                        </Typography>
                        <Stack className="variants-container">
                          {product.variants.slice(0, 2).map((variant, index) => (
                            <Chip
                              key={index}
                              label={variant}
                              size="small"
                              variant="outlined"
                              className="variant-chip"
                            />
                          ))}
                          {product.variants.length > 2 && (
                            <Chip
                              label={`+${product.variants.length - 2}`}
                              size="small"
                              variant="outlined"
                              className="variant-chip"
                            />
                          )}
                        </Stack>
                      </Box>

                      {/* Price Section */}
                      <Box className="price-section">
                        <Stack className="price-container">
                          <Typography variant="h6" className="price-current">
                            ${product.price}
                          </Typography>
                          {product.originalPrice > product.price && (
                            <Typography variant="body2" className="price-original">
                              ${product.originalPrice}
                            </Typography>
                          )}
                        </Stack>
                      </Box>

                      {/* Action Buttons */}
                      <Stack className="action-buttons">
                        <Button
                          variant="contained"
                          startIcon={<CartIcon />}
                          onClick={() => addToCart(product.id)}
                          disabled={!product.inStock}
                          fullWidth
                          className="add-to-cart-button"
                        >
                          {cart.has(product.id) ? 'Added' : 'Add to Cart'}
                        </Button>

                        <Button
                          variant="outlined"
                          size="small"
                          className="details-button"
                        >
                          Details
                        </Button>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* No Results Message */}
            {filteredProducts.length === 0 && (
              <Paper className="no-results-paper">
                <Typography variant="h6" className="no-results-title">
                  No products found
                </Typography>
                <Typography variant="body1" className="no-results-text">
                  Try adjusting your search criteria or filters
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('All');
                    setSelectedBrand('All');
                    setPriceRange([productData.priceRange.min, productData.priceRange.max]);
                  }}
                  className="clear-filters-button"
                >
                  Clear All Filters
                </Button>
              </Paper>
            )}
          </Grid>
        </Grid>

        {/* Floating Action Button for Cart */}
        <Box className="floating-cart">
          <Badge badgeContent={cart.size} color="error">
            <IconButton className="floating-cart-button">
              <CartIcon />
            </IconButton>
          </Badge>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;