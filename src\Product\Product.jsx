import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  IconButton,
  Chip,
  <PERSON>ing,
  FormControl,
  InputLabel,
  Select,
  MenuItem,

  Switch,
  FormControlLabel,
  Card,
  CardContent,
  CardMedia,
  Stack,
  Paper,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,

  Refresh as RefreshIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import './Product.css';
import productData from './productData.json';

const Product = () => {
  // Search and Display States
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('relevance');
  const [viewMode, setViewMode] = useState('grid');

  // User Interaction States
  const [favorites, setFavorites] = useState(new Set());
  const [cart, setCart] = useState(new Set());

  // Configurator States - Control what's visible in product list
  const [showConfig, setShowConfig] = useState({
    showBrand: true,
    showDescription: true,
    showRating: true,
    showVariants: true,
    showOriginalPrice: true,
    showStockBadge: true,
    showSaleBadge: true,
    showFavoriteButton: true,
    showAddToCart: true,
    showDetailsButton: true,
    compactView: false,
    showProductImages: true,
    showReviewCount: true
  });

  // Filter Functions
  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const addToCart = (productId) => {
    const newCart = new Set(cart);
    newCart.add(productId);
    setCart(newCart);
  };

  const filteredProducts = productData.products.filter(product => {
    const matchesSearch = searchQuery === '' ||
                         product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  const handleConfigChange = (configKey) => (event) => {
    setShowConfig(prev => ({
      ...prev,
      [configKey]: event.target.checked
    }));
  };

  const resetConfiguration = () => {
    setShowConfig({
      showBrand: true,
      showDescription: true,
      showRating: true,
      showVariants: true,
      showOriginalPrice: true,
      showStockBadge: true,
      showSaleBadge: true,
      showFavoriteButton: true,
      showAddToCart: true,
      showDetailsButton: true,
      compactView: false,
      showProductImages: true,
      showReviewCount: true
    });
  };

  return (
    <Box className="product-page-container">
      <Container maxWidth="xl">
        {/* Header */}
       

        {/* 3-Panel Layout - Wireframe Style */}
        <Box className="three-panel-layout">
          {/* Left Panel - FILTER */}
          <Paper className="filters-panel">
            <Box className="panel-header">
              <Box>
                <Typography className="panel-title">FILTER</Typography>
                <Typography className="panel-subtitle">Filters</Typography>
              </Box>
            </Box>

            {/* Filter content area - empty for now */}
            <Box className="filter-content">
              <Typography variant="body2" className="filter-placeholder">
                Filter options will be added here
              </Typography>
            </Box>
          </Paper>

          {/* Center Panel - Product */}
          <Paper className="products-main-panel">
            

            {/* Main Products Grid */}
            {filteredProducts.length > 0 ? (
              <Box className={viewMode === 'grid' ? 'products-grid' : 'products-list'}>
                {filteredProducts.map((product) => (
                  <Card
                    key={product.id}
                    className={`product-card ${showConfig.compactView ? 'product-card-compact' : ''} ${product.isFeatured ? 'product-card-featured' : ''} ${product.isOnSale ? 'product-card-sale' : ''}`}
                  >
                    {showConfig.showProductImages && (
                      <Box className="product-image-container">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="product-image"
                          style={{ height: showConfig.compactView ? '150px' : '200px' }}
                        />

                        <Box className="product-badges">
                          {showConfig.showStockBadge && (
                            <span className={`stock-badge ${product.inStock ? 'stock-in' : 'stock-out'}`}>
                              {product.inStock ? 'In Stock' : 'Out of Stock'}
                            </span>
                          )}
                          {showConfig.showSaleBadge && product.originalPrice > product.price && (
                            <span className="stock-badge sale-badge">
                              -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                            </span>
                          )}
                        </Box>

                        {showConfig.showFavoriteButton && (
                          <button
                            className="favorite-button"
                            onClick={() => toggleFavorite(product.id)}
                          >
                            {favorites.has(product.id) ? (
                              <FavoriteIcon className="favorite-active" />
                            ) : (
                              <FavoriteBorderIcon className="favorite-inactive" />
                            )}
                          </button>
                        )}
                      </Box>
                    )}

                    <CardContent className="product-content">
                      {showConfig.showBrand && (
                        <Typography className="product-brand">
                          {product.brand}
                        </Typography>
                      )}

                      <Typography variant="h6" className="product-name">
                        {product.name}
                      </Typography>

                      {showConfig.showDescription && !showConfig.compactView && (
                        <Typography className="product-description">
                          {product.description}
                        </Typography>
                      )}

                      {showConfig.showRating && (
                        <Box className="product-rating">
                          <Rating value={product.rating} precision={0.1} readOnly size="small" />
                          {showConfig.showReviewCount && (
                            <Typography className="rating-text">
                              ({product.reviews})
                            </Typography>
                          )}
                        </Box>
                      )}

                      {showConfig.showVariants && !showConfig.compactView && (
                        <Box className="product-variants">
                          <Typography className="variants-label">Variants:</Typography>
                          <Box className="variants-chips">
                            {product.variants.slice(0, 3).map((variant, index) => (
                              <Chip
                                key={index}
                                label={variant}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            ))}
                            {product.variants.length > 3 && (
                              <Chip
                                label={`+${product.variants.length - 3}`}
                                size="small"
                                variant="outlined"
                                className="variant-chip"
                              />
                            )}
                          </Box>
                        </Box>
                      )}

                      <Box className="product-pricing">
                        <Typography className="price-current">
                          ${product.price}
                        </Typography>
                        {showConfig.showOriginalPrice && product.originalPrice > product.price && (
                          <Typography className="price-original">
                            ${product.originalPrice}
                          </Typography>
                        )}
                      </Box>

                      {!showConfig.compactView && (
                        <Box className="product-actions">
                          {showConfig.showAddToCart && (
                            <Button
                              variant="contained"
                              startIcon={<CartIcon />}
                              onClick={() => addToCart(product.id)}
                              disabled={!product.inStock}
                              className="add-cart-button"
                              fullWidth={!showConfig.showDetailsButton}
                            >
                              {cart.has(product.id) ? 'Added' : 'Add to Cart'}
                            </Button>
                          )}

                          {showConfig.showDetailsButton && (
                            <Button
                              variant="outlined"
                              size="small"
                              className="details-button"
                            >
                              Details
                            </Button>
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Box className="no-results">
                <Typography variant="h6" className="no-results-title">
                  No products found
                </Typography>
                <Typography className="no-results-text">
                  Try adjusting your search criteria or filters
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => {
                    setSearchQuery('');
                  }}
                  className="clear-filters-button"
                >
                  Clear Search
                </Button>
              </Box>
            )}
          </Paper>

          {/* Right Panel - Configurator */}
          <Paper className="configurator-panel">
            <Box className="panel-header">
              <Box>
                <Typography className="panel-title">Configurator</Typography>
                <Typography className="panel-subtitle">Display Settings</Typography>
              </Box>
            </Box>

            {/* Product Information Controls */}
            <Box className="config-section">
              <Typography className="config-label">Product Information</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Brand</Typography>
                <Switch
                  checked={showConfig.showBrand}
                  onChange={handleConfigChange('showBrand')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Description</Typography>
                <Switch
                  checked={showConfig.showDescription}
                  onChange={handleConfigChange('showDescription')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Product Images</Typography>
                <Switch
                  checked={showConfig.showProductImages}
                  onChange={handleConfigChange('showProductImages')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Variants</Typography>
                <Switch
                  checked={showConfig.showVariants}
                  onChange={handleConfigChange('showVariants')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Rating & Reviews Controls */}
            <Box className="config-section">
              <Typography className="config-label">Rating & Reviews</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Rating</Typography>
                <Switch
                  checked={showConfig.showRating}
                  onChange={handleConfigChange('showRating')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Review Count</Typography>
                <Switch
                  checked={showConfig.showReviewCount}
                  onChange={handleConfigChange('showReviewCount')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Pricing Controls */}
            <Box className="config-section">
              <Typography className="config-label">Pricing Display</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Original Price</Typography>
                <Switch
                  checked={showConfig.showOriginalPrice}
                  onChange={handleConfigChange('showOriginalPrice')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Badges & Indicators */}
            <Box className="config-section">
              <Typography className="config-label">Badges & Indicators</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Stock Badge</Typography>
                <Switch
                  checked={showConfig.showStockBadge}
                  onChange={handleConfigChange('showStockBadge')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Sale Badge</Typography>
                <Switch
                  checked={showConfig.showSaleBadge}
                  onChange={handleConfigChange('showSaleBadge')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Action Buttons */}
            <Box className="config-section">
              <Typography className="config-label">Action Buttons</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Favorite Button</Typography>
                <Switch
                  checked={showConfig.showFavoriteButton}
                  onChange={handleConfigChange('showFavoriteButton')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Add to Cart</Typography>
                <Switch
                  checked={showConfig.showAddToCart}
                  onChange={handleConfigChange('showAddToCart')}
                  size="small"
                />
              </Box>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Show Details Button</Typography>
                <Switch
                  checked={showConfig.showDetailsButton}
                  onChange={handleConfigChange('showDetailsButton')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Layout Options */}
            <Box className="config-section">
              <Typography className="config-label">Layout Options</Typography>

              <Box className="config-toggle">
                <Typography className="config-toggle-label">Compact View</Typography>
                <Switch
                  checked={showConfig.compactView}
                  onChange={handleConfigChange('compactView')}
                  size="small"
                />
              </Box>
            </Box>

            {/* Configuration Actions */}
            <Box className="config-actions">
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                className="config-button"
                fullWidth
              >
                Save Configuration
              </Button>

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetConfiguration}
                className="config-button-secondary"
                fullWidth
              >
                Reset to Default
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default Product;