import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Ty<PERSON>graphy,
  Button,
  Card,
  CardContent,
  CardMedia,
  Rating,
  Chip,
  Tabs,
  Tab,
  Breadcrumbs,
  Link,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Badge,
  Tooltip,
  Alert,
  LinearProgress,
  Drawer,
  Switch,
  FormControlLabel,
  TextField,
  Slider
} from '@mui/material';
import {
  ShoppingCart,
  Favorite,
  Share,
  LocalShipping,
  Security,
  CheckCircle,
  ArrowBack,
  ArrowForward,
  ZoomIn,
  ExpandMore,
  Settings,
  Visibility,
  VisibilityOff,
  Edit,
  Save,
  Cancel,
  Info,
  Warning,
  Build,
  Speed,
  Star,
  Inventory,
  AttachMoney,
  Category
} from '@mui/icons-material';
import { sampleProducts, adminConfigOptions } from '../data/productData';

const Product = ({ productId = 'AM001', isAdmin = false }) => {
  // State management
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedVehicle, setSelectedVehicle] = useState('');
  const [adminDrawerOpen, setAdminDrawerOpen] = useState(false);
  const [adminConfig, setAdminConfig] = useState({});
  const [editMode, setEditMode] = useState(false);

  // Load product data
  useEffect(() => {
    const product = sampleProducts.find(p => p.id === productId);
    if (product) {
      setSelectedProduct(product);
      setAdminConfig(product.adminConfig);
    }
  }, [productId]);

  // Event handlers
  const handleImageChange = (direction) => {
    if (!selectedProduct) return;
    const totalImages = selectedProduct.media.images.length;

    if (direction === 'next') {
      setSelectedImageIndex((prev) => (prev + 1) % totalImages);
    } else {
      setSelectedImageIndex((prev) => (prev - 1 + totalImages) % totalImages);
    }
  };

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  const handleAddToCart = () => {
    console.log('Adding to cart:', { productId, quantity, selectedVehicle });
    // Implement cart functionality
  };

  const handleAdminConfigChange = (field, value) => {
    setAdminConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveAdminConfig = () => {
    console.log('Saving admin config:', adminConfig);
    setEditMode(false);
    // Implement save functionality
  };

  if (!selectedProduct) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
          Loading product...
        </Typography>
      </Container>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'var(--theme-background-primary)' }}>
      {/* Admin Controls */}
      {isAdmin && (
        <Box sx={{
          position: 'fixed',
          top: 16,
          right: 16,
          zIndex: 1000,
          display: 'flex',
          gap: 1
        }}>
          <Tooltip title="Admin Configuration">
            <IconButton
              onClick={() => setAdminDrawerOpen(true)}
              sx={{
                bgcolor: 'var(--color-primary-brand)',
                color: 'white',
                boxShadow: 'var(--card-shadow)',
                '&:hover': {
                  bgcolor: 'var(--button-primary-hover-bg-brand)'
                }
              }}
            >
              <Settings />
            </IconButton>
          </Tooltip>

          <Tooltip title={adminConfig.isVisible ? 'Visible to Customers' : 'Hidden from Customers'}>
            <IconButton
              sx={{
                bgcolor: adminConfig.isVisible ? 'var(--color-status-success)' : 'var(--color-status-error)',
                color: 'white',
                boxShadow: 'var(--card-shadow)'
              }}
            >
              {adminConfig.isVisible ? <Visibility /> : <VisibilityOff />}
            </IconButton>
          </Tooltip>
        </Box>
      )}

      <Container maxWidth="xl" sx={{ py: 1 }}>
        {/* Compact Breadcrumbs */}
        <Breadcrumbs
          sx={{
            mb: 2,
            py: 1,
            '& .MuiBreadcrumbs-separator': { mx: 1 },
            '& .MuiBreadcrumbs-li': { fontSize: '0.875rem' }
          }}
        >
          <Link color="inherit" href="/" sx={{ textDecoration: 'none' }}>
            Home
          </Link>
          <Link color="inherit" href={`/category/${selectedProduct.category}`} sx={{ textDecoration: 'none' }}>
            {selectedProduct.category.charAt(0).toUpperCase() + selectedProduct.category.slice(1)}
          </Link>
          <Typography color="text.primary" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
            {selectedProduct.name}
          </Typography>
        </Breadcrumbs>

        <Grid container spacing={2}>
        {/* Left Column - Product Images */}
        <Grid item xs={12} md={5}>
          <Box sx={{ position: 'sticky', top: 16 }}>
            {/* Main Product Image */}
            <Card
              elevation={2}
              sx={{
                mb: 1,
                position: 'relative',
                borderRadius: 'var(--radius-md)',
                overflow: 'hidden'
              }}
            >
              <CardMedia
                component="img"
                height="400"
                image={selectedProduct.media.images[selectedImageIndex]}
                alt={selectedProduct.name}
                sx={{
                  objectFit: 'contain',
                  bgcolor: 'var(--theme-background-secondary)',
                  cursor: 'zoom-in'
                }}
              />

              {/* Compact Navigation */}
              {selectedProduct.media.images.length > 1 && (
                <>
                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      left: 8,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      bgcolor: 'rgba(0, 0, 0, 0.6)',
                      color: 'white',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' }
                    }}
                    onClick={() => handleImageChange('prev')}
                  >
                    <ArrowBack fontSize="small" />
                  </IconButton>

                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      right: 8,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      bgcolor: 'rgba(0, 0, 0, 0.6)',
                      color: 'white',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' }
                    }}
                    onClick={() => handleImageChange('next')}
                  >
                    <ArrowForward fontSize="small" />
                  </IconButton>
                </>
              )}

              {/* Compact Status Badges */}
              <Box sx={{ position: 'absolute', top: 8, left: 8, display: 'flex', gap: 1 }}>
                <Chip
                  label={selectedProduct.inventory.inStock ? 'In Stock' : 'Out of Stock'}
                  color={selectedProduct.inventory.inStock ? 'success' : 'error'}
                  size="small"
                />
                {selectedProduct.pricing.discountPercentage > 0 && (
                  <Chip
                    label={`${selectedProduct.pricing.discountPercentage}% OFF`}
                    color="error"
                    size="small"
                  />
                )}
              </Box>

              {/* Image Counter */}
              {selectedProduct.media.images.length > 1 && (
                <Chip
                  label={`${selectedImageIndex + 1}/${selectedProduct.media.images.length}`}
                  size="small"
                  sx={{
                    position: 'absolute',
                    bottom: 8,
                    right: 8,
                    bgcolor: 'rgba(0, 0, 0, 0.6)',
                    color: 'white'
                  }}
                />
              )}
            </Card>

            {/* Compact Thumbnail Strip */}
            {selectedProduct.media.images.length > 1 && (
              <Box sx={{ display: 'flex', gap: 0.5, overflowX: 'auto', pb: 1 }}>
                {selectedProduct.media.images.map((image, index) => (
                  <Card
                    key={index}
                    sx={{
                      minWidth: 60,
                      height: 60,
                      cursor: 'pointer',
                      border: selectedImageIndex === index
                        ? '2px solid var(--color-primary-brand)'
                        : '1px solid var(--theme-border-color)',
                      transition: 'var(--transition-fast)',
                      '&:hover': {
                        borderColor: 'var(--color-primary-brand)'
                      }
                    }}
                    onClick={() => setSelectedImageIndex(index)}
                  >
                    <CardMedia
                      component="img"
                      height="60"
                      image={image}
                      alt={`${selectedProduct.name} ${index + 1}`}
                      sx={{ objectFit: 'contain' }}
                    />
                  </Card>
                ))}
              </Box>
            )}
          </Box>
        </Grid>

        {/* Right Column - Product Details */}
        <Grid item xs={12} md={7}>
          <Box>
            {/* Compact Product Header */}
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                <Typography variant="h5" component="h1" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
                  {selectedProduct.name}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <IconButton size="small" color="primary">
                    <Favorite fontSize="small" />
                  </IconButton>
                  <IconButton size="small" color="primary">
                    <Share fontSize="small" />
                  </IconButton>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                <Typography variant="subtitle1" color="text.secondary">
                  {selectedProduct.brand}
                </Typography>
                <Chip
                  label={selectedProduct.sku}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.75rem' }}
                />
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Rating
                    value={selectedProduct.reviews.averageRating}
                    precision={0.1}
                    readOnly
                    size="small"
                  />
                  <Typography variant="caption" color="text.secondary">
                    ({selectedProduct.reviews.totalReviews})
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Compact Pricing */}
            <Card elevation={1} sx={{ mb: 2, p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  {selectedProduct.pricing.discountPercentage > 0 && (
                    <Typography
                      variant="body1"
                      sx={{
                        textDecoration: 'line-through',
                        color: 'text.secondary'
                      }}
                    >
                      ${selectedProduct.pricing.msrp.toFixed(2)}
                    </Typography>
                  )}
                  <Typography
                    variant="h4"
                    sx={{
                      color: 'var(--color-primary-brand)',
                      fontWeight: 'bold'
                    }}
                  >
                    ${selectedProduct.pricing.retailPrice.toFixed(2)}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Inventory fontSize="small" color="action" />
                  <Typography variant="body2" color="text.secondary">
                    {selectedProduct.inventory.quantity} in stock
                  </Typography>
                  {selectedProduct.inventory.quantity <= selectedProduct.inventory.lowStockThreshold && (
                    <Chip label="Low Stock" color="warning" size="small" />
                  )}
                </Box>
              </Box>
            </Card>

            {/* Compact Vehicle Compatibility & Actions */}
            <Grid container spacing={2} sx={{ mb: 2 }}>
              {/* Vehicle Compatibility */}
              {selectedProduct.compatibility.vehicles && selectedProduct.compatibility.vehicles.length > 0 && (
                <Grid item xs={12} md={6}>
                  <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Build color="primary" fontSize="small" />
                      Vehicle Compatibility
                    </Typography>

                    <FormControl fullWidth size="small">
                      <InputLabel>Select Vehicle</InputLabel>
                      <Select
                        value={selectedVehicle}
                        label="Select Vehicle"
                        onChange={(e) => setSelectedVehicle(e.target.value)}
                      >
                        {selectedProduct.compatibility.vehicles.map((vehicle, index) => (
                          <MenuItem key={index} value={`${vehicle.make}-${vehicle.model}`}>
                            {vehicle.make} {vehicle.model} ({vehicle.years[0]}-{vehicle.years[vehicle.years.length - 1]})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    {selectedVehicle && (
                      <Alert severity="success" sx={{ mt: 1, py: 0.5 }}>
                        <Typography variant="caption">
                          ✓ Compatible with selected vehicle
                        </Typography>
                      </Alert>
                    )}
                  </Card>
                </Grid>
              )}

              {/* Add to Cart Section */}
              <Grid item xs={12} md={6}>
                <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <FormControl sx={{ minWidth: 80 }} size="small">
                      <InputLabel>Qty</InputLabel>
                      <Select
                        value={quantity}
                        label="Qty"
                        onChange={(e) => setQuantity(e.target.value)}
                      >
                        {[...Array(Math.min(10, selectedProduct.inventory.quantity))].map((_, i) => (
                          <MenuItem key={i + 1} value={i + 1}>
                            {i + 1}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    <Button
                      variant="contained"
                      size="medium"
                      startIcon={<ShoppingCart />}
                      onClick={handleAddToCart}
                      disabled={!selectedProduct.inventory.inStock}
                      sx={{
                        flex: 1,
                        bgcolor: 'var(--color-primary-brand)',
                        '&:hover': {
                          bgcolor: 'var(--button-primary-hover-bg-brand)'
                        }
                      }}
                    >
                      Add to Cart
                    </Button>
                  </Box>
                </Card>
              </Grid>
            </Grid>

            {/* Compact Features & Shipping */}
            <Grid container spacing={2}>
              {/* Key Features */}
              <Grid item xs={12} md={8}>
                <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Speed color="primary" fontSize="small" />
                    Key Features
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedProduct.features.slice(0, 4).map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        variant="outlined"
                        icon={<CheckCircle />}
                        sx={{ fontSize: '0.75rem' }}
                      />
                    ))}
                    {selectedProduct.features.length > 4 && (
                      <Chip
                        label={`+${selectedProduct.features.length - 4} more`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.75rem' }}
                      />
                    )}
                  </Box>
                </Card>
              </Grid>

              {/* Shipping Information */}
              <Grid item xs={12} md={4}>
                <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocalShipping color="primary" fontSize="small" />
                    Shipping
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircle color="success" fontSize="small" />
                      <Typography variant="caption">
                        {selectedProduct.shipping.freeShippingEligible ? "Free Shipping" : "Standard Rates"}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Security color="primary" fontSize="small" />
                      <Typography variant="caption">
                        30-Day Returns
                      </Typography>
                    </Box>
                  </Box>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>

        {/* Compact Product Information Tabs */}
        <Box sx={{ mt: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minHeight: 40,
                fontSize: '0.875rem',
                textTransform: 'none'
              }
            }}
          >
            <Tab label="Description" />
            <Tab label="Specifications" />
            <Tab label="Compatibility" />
            <Tab label="Reviews" />
            <Tab label="Installation" />
          </Tabs>

          <Box sx={{ mt: 2 }}>
            {/* Description Tab */}
            {activeTab === 0 && (
              <Card elevation={1} sx={{ p: 2 }}>
                <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
                  {selectedProduct.description}
                </Typography>

                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  Key Benefits
                </Typography>
                <Grid container spacing={1}>
                  {selectedProduct.features.map((feature, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                        <CheckCircle color="success" fontSize="small" sx={{ mt: 0.2 }} />
                        <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                          {feature}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Card>
            )}

            {/* Specifications Tab */}
            {activeTab === 1 && (
              <Card elevation={1} sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Technical Specifications
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableBody>
                      {Object.entries(selectedProduct.specifications).map(([key, value]) => (
                        <TableRow key={key}>
                          <TableCell
                            component="th"
                            scope="row"
                            sx={{
                              fontWeight: 'medium',
                              width: '40%',
                              fontSize: '0.875rem',
                              py: 1
                            }}
                          >
                            {key}
                          </TableCell>
                          <TableCell sx={{ fontSize: '0.875rem', py: 1 }}>
                            {value}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            )}

          {/* Compatibility Tab */}
          {activeTab === 2 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Vehicle Compatibility
              </Typography>

              {selectedProduct.compatibility.vehicles.map((vehicle, index) => (
                <Accordion key={index}>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography variant="subtitle1">
                      {vehicle.make} {vehicle.model}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Model Years:
                        </Typography>
                        <Typography variant="body2">
                          {vehicle.years.join(', ')}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Compatible Engines:
                        </Typography>
                        {vehicle.engines.map((engine, engineIndex) => (
                          <Typography key={engineIndex} variant="body2">
                            • {engine}
                          </Typography>
                        ))}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}

              {selectedProduct.compatibility.oem_numbers && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    OEM Part Numbers:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedProduct.compatibility.oem_numbers.map((number, index) => (
                      <Chip key={index} label={number} variant="outlined" size="small" />
                    ))}
                  </Box>
                </Box>
              )}
            </Card>
          )}

          {/* Reviews Tab */}
          {activeTab === 3 && (
            <Card sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Rating
                  value={selectedProduct.reviews.averageRating}
                  precision={0.1}
                  readOnly
                />
                <Typography variant="h6">
                  {selectedProduct.reviews.averageRating} out of 5
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ({selectedProduct.reviews.totalReviews} reviews)
                </Typography>
              </Box>

              {/* Rating Distribution */}
              <Box sx={{ mb: 3 }}>
                {[5, 4, 3, 2, 1].map((rating) => (
                  <Box key={rating} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 60 }}>
                      {rating} stars
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(selectedProduct.reviews.ratingDistribution[rating] / selectedProduct.reviews.totalReviews) * 100}
                      sx={{ flex: 1, height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body2" sx={{ minWidth: 40 }}>
                      {selectedProduct.reviews.ratingDistribution[rating]}
                    </Typography>
                  </Box>
                ))}
              </Box>

              <Button variant="outlined" startIcon={<Edit />}>
                Write a Review
              </Button>
            </Card>
          )}

          {/* Installation Tab */}
          {activeTab === 4 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Installation Information
              </Typography>

              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Professional installation recommended. Installation time: approximately 30-45 minutes.
                </Typography>
              </Alert>

              {selectedProduct.media.documents && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Available Documents:
                  </Typography>
                  <List>
                    {selectedProduct.media.documents.map((doc, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Info color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={doc.title}
                          secondary={`${doc.type} Document`}
                        />
                        <Button variant="outlined" size="small">
                          Download
                        </Button>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Card>
          )}
        </Box>
      </Box>

      {/* Admin Configuration Drawer */}
      {isAdmin && (
        <Drawer
          anchor="right"
          open={adminDrawerOpen}
          onClose={() => setAdminDrawerOpen(false)}
          sx={{
            '& .MuiDrawer-paper': {
              width: 400,
              p: 3
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6">
              Admin Configuration
            </Typography>
            <Box>
              {editMode ? (
                <>
                  <IconButton onClick={saveAdminConfig} color="primary">
                    <Save />
                  </IconButton>
                  <IconButton onClick={() => setEditMode(false)}>
                    <Cancel />
                  </IconButton>
                </>
              ) : (
                <IconButton onClick={() => setEditMode(true)}>
                  <Edit />
                </IconButton>
              )}
            </Box>
          </Box>

          {/* Visibility Settings */}
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Visibility />
                Visibility Settings
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {adminConfigOptions.visibilityFields.map((field) => (
                <FormControlLabel
                  key={field.key}
                  control={
                    <Switch
                      checked={adminConfig[field.key] || false}
                      onChange={(e) => handleAdminConfigChange(field.key, e.target.checked)}
                      disabled={!editMode}
                    />
                  }
                  label={field.label}
                  sx={{ display: 'block', mb: 1 }}
                />
              ))}
            </AccordionDetails>
          </Accordion>

          {/* Pricing Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AttachMoney />
                Pricing Visibility
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showMSRP || false}
                    onChange={(e) => handleAdminConfigChange('showMSRP', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show MSRP"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showDealerPrice || false}
                    onChange={(e) => handleAdminConfigChange('showDealerPrice', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Dealer Price"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showDiscount || true}
                    onChange={(e) => handleAdminConfigChange('showDiscount', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Discount Badge"
                sx={{ display: 'block', mb: 1 }}
              />
            </AccordionDetails>
          </Accordion>

          {/* Inventory Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Inventory />
                Inventory Display
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showStockCount || true}
                    onChange={(e) => handleAdminConfigChange('showStockCount', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Stock Count"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showLowStockWarning || true}
                    onChange={(e) => handleAdminConfigChange('showLowStockWarning', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Low Stock Warning"
                sx={{ display: 'block', mb: 1 }}
              />
            </AccordionDetails>
          </Accordion>

          {/* Feature Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Category />
                Feature Display
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showCompatibility || true}
                    onChange={(e) => handleAdminConfigChange('showCompatibility', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Compatibility Section"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showReviews || true}
                    onChange={(e) => handleAdminConfigChange('showReviews', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Reviews"
                sx={{ display: 'block', mb: 1 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={adminConfig.showInstallation || true}
                    onChange={(e) => handleAdminConfigChange('showInstallation', e.target.checked)}
                    disabled={!editMode}
                  />
                }
                label="Show Installation Info"
                sx={{ display: 'block', mb: 1 }}
              />
            </AccordionDetails>
          </Accordion>

          {/* Display Order */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Display Order
            </Typography>
            <Slider
              value={adminConfig.displayOrder || 1}
              onChange={(_, value) => handleAdminConfigChange('displayOrder', value)}
              min={1}
              max={100}
              marks
              valueLabelDisplay="auto"
              disabled={!editMode}
            />
          </Box>

          {/* Product Status */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'var(--theme-background-primary)', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Product Status
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Created: {new Date(adminConfig.createdDate).toLocaleDateString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last Modified: {new Date(adminConfig.lastModified).toLocaleDateString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Modified By: {adminConfig.lastModifiedBy}
            </Typography>
          </Box>
        </Drawer>
      )}
      </Container>
    </Box>
  );
};

export default Product;