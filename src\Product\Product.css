/* Product Page - 3 Panel Layout */
/* Using only CSS variables from ThemeContext.jsx */

.product-page-container {
  min-height: 100vh;
  background-color: var(--theme-background-primary);
  color: var(--color-text-default);
  padding: var(--spacing-lg);
}

.product-page-header {
  margin-bottom: var(--spacing-xl);
}

.product-page-title {
  color: var(--color-text-heading);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
}

.search-section {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.search-container {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.search-icon {
  color: var(--color-text-muted);
  margin-right: var(--spacing-sm);
}

.search-button {
  background-color: var(--button-primary-bg);
  color: var(--button-color);
}

.search-button:hover {
  background-color: var(--button-primary-hover-bg);
}

/* 3-Panel Layout - Wireframe Style */
.three-panel-layout {
  display: grid;
  grid-template-columns: 280px 1fr 300px;
  gap: var(--spacing-lg);
  min-height: 85vh;
  align-items: start;
}

/* Left Panel - FILTER */
.filters-panel {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-lg);
  height: fit-content;
  position: sticky;
  top: var(--spacing-lg);
  border: 2px solid var(--theme-border-color);
}

/* Center Panel - Product */
.products-main-panel {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-lg);
  border: 2px solid var(--theme-border-color);
  min-height: 85vh;
}

/* Right Panel - Configurator */
.configurator-panel {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-lg);
  height: fit-content;
  position: sticky;
  top: var(--spacing-lg);
  border: 2px solid var(--theme-border-color);
}

/* Panel Headers */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 3px solid var(--color-primary-brand);
}

.panel-title {
  color: var(--color-text-heading);
  font-weight: var(--font-weight-bold);
  font-size: 1.25rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.panel-subtitle {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-top: calc(var(--spacing-xs) / 2);
}

.filters-title {
  color: var(--color-text-heading);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--theme-border-color);
}

.filter-group {
  margin-bottom: var(--spacing-lg);
}

.filter-label {
  color: var(--color-text-default);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  display: block;
}

.filter-chips-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-chip {
  border-color: var(--theme-border-color);
  color: var(--color-text-secondary);
  justify-content: flex-start;
}

.filter-chip:hover {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
}

.filter-chip-active {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
  border-color: var(--color-primary-brand);
}

.price-range-container {
  margin-top: var(--spacing-md);
}

.price-range-text {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-top: var(--spacing-sm);
}

/* Center Panel - Products */
.products-panel {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-lg);
}

.products-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--theme-border-color);
}

.products-count {
  color: var(--color-text-default);
  font-weight: var(--font-weight-medium);
}

.toolbar-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.view-toggle {
  display: flex;
  border: 1px solid var(--theme-border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.view-button {
  padding: var(--spacing-sm);
  border: none;
  background-color: transparent;
  color: var(--color-text-muted);
  cursor: pointer;
  transition: var(--transition-ease-in-out);
}

.view-button:hover {
  background-color: var(--theme-background-secondary);
}

.view-button-active {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
}

/* Main Product Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-md) 0;
}

/* Product Grid Toolbar */
.products-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--theme-background-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--theme-border-color);
}

/* Enhanced Product Cards */
.product-card {
  background-color: var(--theme-background-secondary);
  border: 1px solid var(--theme-border-color);
  border-radius: var(--card-border-radius);
  overflow: hidden;
  transition: var(--transition-ease-in-out);
  margin-bottom: var(--spacing-md);
  display: flex;
  flex-direction: column;
  position: relative;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--color-primary-brand);
}

.product-card-compact {
  margin-bottom: var(--spacing-sm);
}

.product-card-featured {
  border: 2px solid var(--color-primary-brand);
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--theme-background-secondary) 100%);
}

.product-card-sale {
  border-color: var(--color-status-error);
  position: relative;
}

.product-card-sale::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-status-error), var(--color-status-warning));
  z-index: 1;
}

.product-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: var(--theme-background-primary);
}

.product-badges {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stock-badge {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  padding: calc(var(--spacing-xs) / 2) var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.stock-in {
  background-color: var(--color-status-success);
  color: var(--color-white);
}

.stock-out {
  background-color: var(--color-status-error);
  color: var(--color-white);
}

.sale-badge {
  background-color: var(--color-status-error);
  color: var(--color-white);
  font-weight: var(--font-weight-bold);
}

.favorite-button {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-ease-in-out);
}

.favorite-button:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.favorite-active {
  color: var(--color-status-error);
}

.favorite-inactive {
  color: var(--color-text-muted);
}

.product-content {
  padding: var(--spacing-md);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-brand {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: calc(var(--spacing-xs) / 2);
}

.product-name {
  color: var(--color-text-heading);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
  font-size: 1rem;
}

.product-description {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-md);
  flex: 1;
  line-height: 1.4;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.rating-text {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.product-variants {
  margin-bottom: var(--spacing-md);
}

.variants-label {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
  margin-bottom: var(--spacing-xs);
  display: block;
}

.variants-chips {
  display: flex;
  gap: calc(var(--spacing-xs) / 2);
  flex-wrap: wrap;
}

.variant-chip {
  font-size: 0.7rem;
  height: 20px;
  border-color: var(--theme-border-color);
  color: var(--color-text-secondary);
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.price-current {
  color: var(--color-primary-brand);
  font-weight: var(--font-weight-bold);
  font-size: 1.125rem;
}

.price-original {
  color: var(--color-text-muted);
  text-decoration: line-through;
  font-size: 0.875rem;
}

.product-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: auto;
}

.add-cart-button {
  flex: 1;
  background-color: var(--button-primary-bg);
  color: var(--button-color);
}

.add-cart-button:hover {
  background-color: var(--button-primary-hover-bg);
}

.add-cart-button:disabled {
  background-color: var(--color-text-muted);
  color: var(--color-white);
}

.details-button {
  border-color: var(--color-primary-brand);
  color: var(--color-primary-brand);
}

.details-button:hover {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
}

/* Right Panel - Configurator */
.configurator-panel {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  padding: var(--spacing-lg);
  height: fit-content;
  position: sticky;
  top: var(--spacing-lg);
}

.configurator-title {
  color: var(--color-text-heading);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--theme-border-color);
}

.config-section {
  margin-bottom: var(--spacing-lg);
}

.config-label {
  color: var(--color-text-default);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  display: block;
}

.config-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.config-toggle-label {
  color: var(--color-text-default);
  font-size: 0.875rem;
}

.config-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--theme-border-color);
}

.config-button {
  background-color: var(--button-primary-bg);
  color: var(--button-color);
}

.config-button:hover {
  background-color: var(--button-primary-hover-bg);
}

.config-button-secondary {
  border-color: var(--color-primary-brand);
  color: var(--color-primary-brand);
}

.config-button-secondary:hover {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
}

/* No Results */
.no-results {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--color-text-secondary);
}

.no-results-title {
  color: var(--color-text-heading);
  margin-bottom: var(--spacing-md);
}

.no-results-text {
  margin-bottom: var(--spacing-lg);
}

.clear-filters-button {
  background-color: var(--button-primary-bg);
  color: var(--button-color);
}

.clear-filters-button:hover {
  background-color: var(--button-primary-hover-bg);
}

/* Responsive Design for 3-Panel Layout */
@media (max-width: 1400px) {
  .three-panel-layout {
    grid-template-columns: 260px 1fr 280px;
    gap: var(--spacing-md);
  }
}

@media (max-width: 1200px) {
  .three-panel-layout {
    grid-template-columns: 240px 1fr 260px;
    gap: var(--spacing-md);
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 992px) {
  .three-panel-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .filters-panel,
  .configurator-panel {
    position: static;
    margin-bottom: var(--spacing-md);
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .three-panel-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .filters-panel,
  .configurator-panel,
  .products-main-panel {
    position: static;
    margin-bottom: var(--spacing-md);
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 480px) {
  .product-page-container {
    padding: var(--spacing-md);
  }

  .search-container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-card {
    margin-bottom: var(--spacing-sm);
  }
}