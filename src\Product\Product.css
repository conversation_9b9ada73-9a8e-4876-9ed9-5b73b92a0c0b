/* Product Component Custom Styles */
/* Using only CSS variables from ThemeContext.jsx */

.product-container {
  min-height: 100vh;
  background-color: var(--theme-background-primary);
  color: var(--color-text-default);
}

.product-header {
  margin-bottom: var(--spacing-xl);
}

.product-title {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-heading);
  font-weight: var(--font-weight-bold);
}

.search-paper {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
}

.search-container {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.search-icon {
  margin-right: var(--spacing-sm);
  color: var(--color-text-muted);
}

.search-button {
  background-color: var(--button-primary-bg);
}

.search-button:hover {
  background-color: var(--button-primary-hover-bg);
}

.filters-sidebar {
  padding: var(--spacing-lg);
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
}

.filters-title {
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-heading);
  font-weight: var(--font-weight-medium);
}

.filter-section {
  margin-bottom: var(--spacing-lg);
}

.filter-label {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-default);
  font-weight: var(--font-weight-medium);
}

.price-range-text {
  margin-top: var(--spacing-sm);
  color: var(--color-text-secondary);
}

.quick-filters {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.quick-filter-chip {
  border-color: var(--color-primary-brand);
  color: var(--color-primary-brand);
}

.quick-filter-chip:hover {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
}

.quick-filter-sale {
  border-color: var(--color-status-error);
  color: var(--color-status-error);
}

.quick-filter-sale:hover {
  background-color: var(--color-status-error);
  color: var(--color-white);
}

.quick-filter-rated {
  border-color: var(--color-rating-star);
  color: var(--color-rating-star);
}

.quick-filter-rated:hover {
  background-color: var(--color-rating-star);
  color: var(--color-white);
}

.toolbar-paper {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.view-toggle {
  display: flex;
}

.view-button-active {
  color: var(--color-primary-brand);
}

.view-button-inactive {
  color: var(--color-text-muted);
}

.product-card {
  height: 100%;
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--card-border-radius);
  transition: var(--transition-ease-in-out);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image-container {
  position: relative;
}

.product-image {
  object-fit: cover;
  background-color: var(--theme-background-secondary);
}

.stock-badge {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

.stock-in {
  background-color: var(--color-status-success);
  color: var(--color-white);
}

.stock-out {
  background-color: var(--color-status-error);
  color: var(--color-white);
}

.favorite-button {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: rgba(255, 255, 255, 0.9);
}

.favorite-button:hover {
  background-color: rgba(255, 255, 255, 1);
}

.favorite-active {
  color: var(--color-status-error);
}

.favorite-inactive {
  color: var(--color-text-muted);
}

.sale-badge {
  position: absolute;
  bottom: var(--spacing-sm);
  left: var(--spacing-sm);
  background-color: var(--color-status-error);
  color: var(--color-white);
  font-weight: var(--font-weight-bold);
}

.product-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md);
}

.product-brand {
  color: var(--color-text-secondary);
  margin-bottom: calc(var(--spacing-xs) / 2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-name {
  color: var(--color-text-heading);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.product-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-md);
  flex: 1;
}

.rating-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.rating-text {
  margin-left: var(--spacing-sm);
  color: var(--color-text-secondary);
}

.variants-section {
  margin-bottom: var(--spacing-md);
}

.variants-label {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
  display: block;
}

.variants-container {
  display: flex;
  gap: calc(var(--spacing-xs) / 2);
  flex-wrap: wrap;
}

.variant-chip {
  font-size: 0.7rem;
  height: 20px;
  border-color: var(--theme-border-color);
  color: var(--color-text-secondary);
}

.price-section {
  margin-bottom: var(--spacing-md);
}

.price-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.price-current {
  color: var(--color-primary-brand);
  font-weight: var(--font-weight-bold);
}

.price-original {
  color: var(--color-text-muted);
  text-decoration: line-through;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.add-to-cart-button {
  background-color: var(--button-primary-bg);
  color: var(--button-color);
}

.add-to-cart-button:hover {
  background-color: var(--button-primary-hover-bg);
}

.add-to-cart-button:disabled {
  background-color: var(--color-text-muted);
  color: var(--color-white);
}

.details-button {
  border-color: var(--color-primary-brand);
  color: var(--color-primary-brand);
}

.details-button:hover {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
}

.no-results-paper {
  padding: var(--spacing-xl);
  text-align: center;
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
}

.no-results-title {
  color: var(--color-text-heading);
  margin-bottom: var(--spacing-md);
}

.no-results-text {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
}

.clear-filters-button {
  background-color: var(--button-primary-bg);
}

.clear-filters-button:hover {
  background-color: var(--button-primary-hover-bg);
}

.floating-cart {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.floating-cart-button {
  background-color: var(--color-primary-brand);
  color: var(--color-white);
  width: 56px;
  height: 56px;
  transition: var(--transition-ease-in-out);
}

.floating-cart-button:hover {
  background-color: var(--button-primary-hover-bg);
  transform: scale(1.1);
}
