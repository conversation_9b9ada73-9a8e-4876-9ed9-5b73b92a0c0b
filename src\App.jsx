import React from 'react';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { CssBaseline } from '@mui/material';
import { createOverrideTheme } from './theme';
import Product from './Product/Product';

const AppContent = () => {
  const { getMuiPaletteConfig } = useTheme();
  const muiTheme = React.useMemo(() => createOverrideTheme(getMuiPaletteConfig()), [getMuiPaletteConfig]);

  return (
    <MuiThemeProvider theme={muiTheme}>
      <CssBaseline />
      <Product />
    </MuiThemeProvider>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

export default App
