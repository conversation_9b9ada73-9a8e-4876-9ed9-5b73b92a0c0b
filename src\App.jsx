import { useState } from 'react'
import React from 'react';
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { CssBaseline, Box } from '@mui/material';
import { createOverrideTheme } from './theme'; // Corrected import path
import ProductPage from './Product/Product';


const AppContent = () => {
  const { getMuiPaletteConfig } = useTheme();
  const muiTheme = React.useMemo(() => createOverrideTheme(getMuiPaletteConfig()), [getMuiPaletteConfig]);


  return (
   
         <MuiThemeProvider theme={muiTheme}>
   
          <ProductPage />
  
    </MuiThemeProvider>
     
     
    
  )
}

function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}
export default App
