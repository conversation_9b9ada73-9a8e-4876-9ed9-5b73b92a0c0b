import React from 'react';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { CssBaseline, Box, Button, Typography } from '@mui/material';
import { createOverrideTheme } from './theme';
import ProductPage from './Product/Product';

const AppContent = () => {
  const { getMuiPaletteConfig } = useTheme();
  const muiTheme = React.useMemo(() => createOverrideTheme(getMuiPaletteConfig()), [getMuiPaletteConfig]);
  const [isAdmin, setIsAdmin] = React.useState(false);

  return (
    <MuiThemeProvider theme={muiTheme}>
      <CssBaseline />

      {/* Demo Toggle for Admin Mode */}
      <Box sx={{
        position: 'fixed',
        top: 20,
        left: 20,
        zIndex: 1000,
        display: 'flex',
        gap: 2,
        alignItems: 'center'
      }}>
        <Typography variant="body2">
          Mode:
        </Typography>
        <Button
          variant={!isAdmin ? 'contained' : 'outlined'}
          size="small"
          onClick={() => setIsAdmin(false)}
        >
          Customer
        </Button>
        <Button
          variant={isAdmin ? 'contained' : 'outlined'}
          size="small"
          onClick={() => setIsAdmin(true)}
        >
          Admin
        </Button>
      </Box>

      <ProductPage productId="AM001" isAdmin={isAdmin} />
    </MuiThemeProvider>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

export default App;
